DECLARE @SubmitterEmail NVARCHAR(255);

-- Lookup user details
SELECT 
    @SubmitterUserID = orq.user_id,
    @SubmitterEmail = u.email
FROM [Iss_ppe_consumables_portal].[dbo].[order_requests] orq
JOIN [users] u
    ON orq.user_id = u.user_id
WHERE orq.request_id = @RequestID;

BEGIN TRANSACTION;
BEGIN TRY
    ------------------------
    -- 1️⃣ Update parent request
    ------------------------
    UPDATE [Iss_ppe_consumables_portal].[dbo].[order_requests]
    SET site_id = @TargetSiteID,
        required_date = @RequiredDate
    WHERE request_id = @RequestID;

    ------------------------
    -- 2️⃣ Soft-delete old statuses
    ------------------------
    UPDATE s
    SET is_active = 0, is_latest = 0
    FROM [Iss_ppe_consumables_portal].[dbo].[status] s
    INNER JOIN [Iss_ppe_consumables_portal].[dbo].[order_request_items] i 
        ON s.request_item_id = i.id
    WHERE i.request_id = @RequestID;

    ------------------------
    -- 3️⃣ Parse incoming JSON into table
    ------------------------
    DECLARE @Items TABLE (
        RequestItemID NVARCHAR(50) NULL,
        ProductID INT,
        Size NVARCHAR(50),
        Quantity INT,
        NameForPrinting NVARCHAR(255),
        Comments NVARCHAR(MAX),
        SizeID INT
    );

    INSERT INTO @Items(RequestItemID, ProductID, Size, Quantity, NameForPrinting, Comments, SizeID)
    SELECT DISTINCT
        JSON_VALUE(value, '$.RequestItemID'),
        CAST(JSON_VALUE(value, '$.ProductID') AS INT),
        JSON_VALUE(value, '$.Size'),
        CAST(JSON_VALUE(value, '$.Quantity') AS INT),
        JSON_VALUE(value, '$.NameForPrinting'),
        JSON_VALUE(value, '$.Comments'),
                TRY_CAST(NULLIF(JSON_VALUE(value, '$.SizeID'), '') AS INT)
    FROM OPENJSON(@ItemsJSON);

SELECT 
    id as RequestItemID,
    product_id,
    quantity,
    is_active
FROM [Iss_ppe_consumables_portal].[dbo].[order_request_items]
WHERE request_id = @RequestID AND is_active = 1
ORDER BY id;

    ------------------------
    -- 4️⃣ Update existing numeric items
    ------------------------
    UPDATE ori
    SET
        product_id = i.ProductID,
        size = i.Size,
        quantity = i.Quantity,
        name_for_printing = i.NameForPrinting,
        comments = i.Comments,
        size_id = CASE 
        WHEN i.SizeID IS NOT NULL AND EXISTS (SELECT 1 FROM [Iss_ppe_consumables_portal].[dbo].[product_sizes] WHERE id = i.SizeID)
        THEN i.SizeID 
        ELSE NULL
        END,
        is_active = 1
    FROM [Iss_ppe_consumables_portal].[dbo].[order_request_items] ori
    JOIN @Items i ON ori.id = TRY_CAST(i.RequestItemID AS INT)
    WHERE TRY_CAST(i.RequestItemID AS INT) IS NOT NULL;

    ------------------------
    -- 5️⃣ Insert new items and capture IDs
    ------------------------
    -- DECLARE @nextItemNumber INT = (
    -- Select ISNULL(MAX(item_number), 0) 
    -- from [Iss_ppe_consumables_portal].[dbo].[order_request_items] 
    -- where request_id = @RequestID);

    DECLARE @nextItemNumber INT;
    SELECT @nextItemNumber = ISNULL(MAX([item_number]), 0) 
    FROM [Iss_ppe_consumables_portal].[dbo].[order_request_items]
    WHERE [request_id] = @RequestID;

    
    DECLARE @NewItemIDs TABLE (RequestItemID INT);

    INSERT INTO [Iss_ppe_consumables_portal].[dbo].[order_request_items] (
        request_id, product_id, size, quantity, name_for_printing, comments,is_active,item_number, size_id 
    )
    OUTPUT INSERTED.id INTO @NewItemIDs(RequestItemID)
    SELECT
        @RequestID,
        ProductID,
        Size,
        Quantity,
        NameForPrinting,
        Comments,
        1,
        ROW_NUMBER() OVER (ORDER BY (SELECT NULL) + @nextItemNumber),
    CASE 
        WHEN SizeID IS NOT NULL AND EXISTS (SELECT 1 FROM [Iss_ppe_consumables_portal].[dbo].[product_sizes] WHERE id = SizeID)
        THEN SizeID 
        ELSE NULL 
    END
    FROM @Items
    WHERE RequestItemID IS NULL 
   OR RequestItemID = '' 
   OR RequestItemID = 'null'
   OR TRY_CAST(RequestItemID AS INT) IS NULL;

    ------------------------
    -- 6️⃣ Soft-delete removed items
    ------------------------
    UPDATE ori
    SET is_active = 0
    FROM [Iss_ppe_consumables_portal].[dbo].[order_request_items] ori
    WHERE ori.request_id = @RequestID
   AND ori.id NOT IN (
      SELECT RequestItemID FROM @Items WHERE RequestItemID IS NOT NULL
      UNION
      SELECT RequestItemID FROM @NewItemIDs  -- <── exclude newly inserted ones
  );

    ------------------------
    -- 7️⃣ Insert status rows for all affected items
    ------------------------
    DECLARE @ItemResults TABLE(RequestItemID INT);

    -- Updated items
    INSERT INTO @ItemResults(RequestItemID)
    SELECT ori.id
    FROM [Iss_ppe_consumables_portal].[dbo].[order_request_items] ori
    JOIN @Items i
        ON ori.id = TRY_CAST(i.RequestItemID AS INT)
    WHERE TRY_CAST(i.RequestItemID AS INT) IS NOT NULL;

    -- Newly inserted items
    INSERT INTO @ItemResults(RequestItemID)
    SELECT RequestItemID FROM @NewItemIDs;

    -- Insert statuses
    INSERT INTO [Iss_ppe_consumables_portal].[dbo].[status] (
        request_item_id,
        action_id,
        actioned_by,
        actioned_by_email,
        actioned_at,
        comment,
        is_active,
        is_latest
    )
    SELECT
        RequestItemID,
        @action_id,
        @SubmitterUserID,
        @SubmitterEmail,
        GETDATE(),
        @commentOnUpdatingRequest,
        1,
        1
    FROM @ItemResults;

    ------------------------
    -- 8️⃣ Commit
    ------------------------
    COMMIT TRANSACTION;

    ------------------------
    -- 9️⃣ Return result
    ------------------------
    SELECT 
        @RequestID AS UpdatedRequestID,
        (SELECT STRING_AGG(RequestItemID, ', ') FROM @ItemResults) AS UpdatedItemIDs;

END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION;
    THROW;
END CATCH;
