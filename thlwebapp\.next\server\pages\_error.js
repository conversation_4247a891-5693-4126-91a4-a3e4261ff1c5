/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_error";
exports.ids = ["pages/_error"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./pages/_document.js\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.js\");\n/* harmony import */ var private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! private-next-pages/_error */ \"./node_modules/next/dist/pages/_error.js\");\n/* harmony import */ var private_next_pages_error__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__]);\nprivate_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n// @ts-ignore this need to be imported from next/dist to be external\n\n\n\n// Import the app and document modules.\n// @ts-expect-error - replaced by webpack/turbopack loader\n\n// @ts-expect-error - replaced by webpack/turbopack loader\n\n// Import the userland code.\n// @ts-expect-error - replaced by webpack/turbopack loader\n\nconst PagesRouteModule = next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule;\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/_error\",\n        pathname: \"/_error\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_auth_msalProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/auth/msalProvider */ \"./utils/auth/msalProvider.jsx\");\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var _utils_loaders_overlaySpinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/loaders/overlaySpinner */ \"./utils/loaders/overlaySpinner.js\");\n/* harmony import */ var _utils_secureThemeContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/secureThemeContext */ \"./utils/secureThemeContext.js\");\n/* harmony import */ var _utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/rolePermissionsContext */ \"./utils/rolePermissionsContext.js\");\n/* harmony import */ var _utils_serviceCustomerContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/serviceCustomerContext */ \"./utils/serviceCustomerContext.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_utils_auth_msalProvider__WEBPACK_IMPORTED_MODULE_3__]);\n_utils_auth_msalProvider__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\n\nfunction App({ Component, pageProps }) {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const isOnline = useNetwork();\n    function useNetwork() {\n        if (false) {}\n    }\n    ;\n    const closeModal = ()=>{\n        setIsOpen(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isOnline) {\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                classNam: \"no-connection\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"No Internet Connection\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"noConnectionAlertBox\", {\n                        isOpen: isOpen,\n                        closeModal: closeModal\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this);\n        }\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_auth_msalProvider__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_secureThemeContext__WEBPACK_IMPORTED_MODULE_6__.SecureThemeProvider, {\n            initialTheme: pageProps.userData?.theme,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_7__.PermissionsProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__.LoadingProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_serviceCustomerContext__WEBPACK_IMPORTED_MODULE_8__.ServiceCustomerProvider, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_loaders_overlaySpinner__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                                lineNumber: 49,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                                ...pageProps\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                                lineNumber: 50,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                        lineNumber: 48,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                    lineNumber: 47,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.js\n");

/***/ }),

/***/ "./pages/_document.js":
/*!****************************!*\
  !*** ./pages/_document.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return(// <Html lang=\"en\" className=\"dark\">\n    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {}, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_document.js\",\n                lineNumber: 7,\n                columnNumber: 8\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_document.js\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_document.js\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_document.js\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_document.js\",\n        lineNumber: 6,\n        columnNumber: 3\n    }, this));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fZG9jdW1lbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTZEO0FBRTlDLFNBQVNJO0lBQ3RCLE9BQ0Usb0NBQW9DO2tCQUN0Qyw4REFBQ0osK0NBQUlBO1FBQUNLLE1BQUs7OzBCQUNOLDhEQUFDSiwrQ0FBSUE7Ozs7OzBCQUNOLDhEQUFDSzs7a0NBQ0MsOERBQUNKLCtDQUFJQTs7Ozs7a0NBQ0wsOERBQUNDLHFEQUFVQTs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJbkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGx3ZWJhcHAvLi9wYWdlcy9fZG9jdW1lbnQuanM/NTM4YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBIdG1sLCBIZWFkLCBNYWluLCBOZXh0U2NyaXB0IH0gZnJvbSBcIm5leHQvZG9jdW1lbnRcIjtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERvY3VtZW50KCkge1xyXG4gIHJldHVybiAoXHJcbiAgICAvLyA8SHRtbCBsYW5nPVwiZW5cIiBjbGFzc05hbWU9XCJkYXJrXCI+XHJcbiAgPEh0bWwgbGFuZz1cImVuXCI+XHJcbiAgICAgICA8SGVhZCAvPlxyXG4gICAgICA8Ym9keT5cclxuICAgICAgICA8TWFpbiAvPlxyXG4gICAgICAgIDxOZXh0U2NyaXB0IC8+XHJcbiAgICAgIDwvYm9keT5cclxuICAgIDwvSHRtbD5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJIdG1sIiwiSGVhZCIsIk1haW4iLCJOZXh0U2NyaXB0IiwiRG9jdW1lbnQiLCJsYW5nIiwiYm9keSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/_document.js\n");

/***/ }),

/***/ "./utils/auth/authConfig.js":
/*!**********************************!*\
  !*** ./utils/auth/authConfig.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BASE_URL: () => (/* binding */ BASE_URL),\n/* harmony export */   loginRequest: () => (/* binding */ loginRequest),\n/* harmony export */   msalConfig: () => (/* binding */ msalConfig)\n/* harmony export */ });\nconst BASE_URL = `${\"http://localhost:3000\"}/login`;\nconst msalConfig = {\n    auth: {\n        clientId: \"bafc3c7b-820b-4d2d-9a96-9162c4b2c78a\",\n        authority: `https://login.microsoftonline.com/${\"6d90d24f-9602-49e8-8903-eb86dce9656a\"}`,\n        redirectUri: \"/\"\n    },\n    cache: {\n        cacheLocation: \"sessionStorage\",\n        storeAuthStateInCookie: false\n    }\n};\nconst loginRequest = {\n    scopes: [\n        \"user.read\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9hdXRoL2F1dGhDb25maWcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQU8sTUFBTUEsV0FBVyxDQUFDLEVBQUVDLHVCQUFnQyxDQUFDLE1BQU0sQ0FBQyxDQUFDO0FBRTdELE1BQU1HLGFBQWE7SUFDeEJDLE1BQU07UUFDSkMsVUFBVUwsc0NBQWlDO1FBQzNDTyxXQUFXLENBQUMsa0NBQWtDLEVBQUVQLHNDQUFpQyxDQUFDLENBQUM7UUFDbkZTLGFBQWE7SUFDZjtJQUNBQyxPQUFPO1FBQ0xDLGVBQWU7UUFDZkMsd0JBQXdCO0lBQzFCO0FBQ0YsRUFBRTtBQUVLLE1BQU1DLGVBQWU7SUFDMUJDLFFBQVE7UUFBQztLQUFZO0FBQ3ZCLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGx3ZWJhcHAvLi91dGlscy9hdXRoL2F1dGhDb25maWcuanM/YWM5ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgQkFTRV9VUkwgPSBgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19CQVNFX1VSTH0vbG9naW5gO1xyXG5cclxuZXhwb3J0IGNvbnN0IG1zYWxDb25maWcgPSB7XHJcbiAgYXV0aDoge1xyXG4gICAgY2xpZW50SWQ6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0NMSUVOVF9JRCxcclxuICAgIGF1dGhvcml0eTogYGh0dHBzOi8vbG9naW4ubWljcm9zb2Z0b25saW5lLmNvbS8ke3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1RFTkFOVF9JRH1gLFxyXG4gICAgcmVkaXJlY3RVcmk6IFwiL1wiLFxyXG4gIH0sXHJcbiAgY2FjaGU6IHtcclxuICAgIGNhY2hlTG9jYXRpb246IFwic2Vzc2lvblN0b3JhZ2VcIiwgLy8gQ2hhbmdlZCBmcm9tIGxvY2FsU3RvcmFnZVxyXG4gICAgc3RvcmVBdXRoU3RhdGVJbkNvb2tpZTogZmFsc2UsXHJcbiAgfSxcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCBsb2dpblJlcXVlc3QgPSB7XHJcbiAgc2NvcGVzOiBbXCJ1c2VyLnJlYWRcIl0sXHJcbn07XHJcbiJdLCJuYW1lcyI6WyJCQVNFX1VSTCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19CQVNFX1VSTCIsIm1zYWxDb25maWciLCJhdXRoIiwiY2xpZW50SWQiLCJORVhUX1BVQkxJQ19DTElFTlRfSUQiLCJhdXRob3JpdHkiLCJORVhUX1BVQkxJQ19URU5BTlRfSUQiLCJyZWRpcmVjdFVyaSIsImNhY2hlIiwiY2FjaGVMb2NhdGlvbiIsInN0b3JlQXV0aFN0YXRlSW5Db29raWUiLCJsb2dpblJlcXVlc3QiLCJzY29wZXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./utils/auth/authConfig.js\n");

/***/ }),

/***/ "./utils/auth/msalProvider.jsx":
/*!*************************************!*\
  !*** ./utils/auth/msalProvider.jsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @azure/msal-browser */ \"@azure/msal-browser\");\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @azure/msal-react */ \"@azure/msal-react\");\n/* harmony import */ var _authConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./authConfig */ \"./utils/auth/authConfig.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__, _azure_msal_react__WEBPACK_IMPORTED_MODULE_2__]);\n([_azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__, _azure_msal_react__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n// components/MsalProvider.tsx\n\n\n\n\n\nconst MsalAuthProvider = ({ children })=>{\n    const msalInstance = new _azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__.PublicClientApplication(_authConfig__WEBPACK_IMPORTED_MODULE_3__.msalConfig);\n    const handlePopup = (event)=>{\n        if (event instanceof Event && event.isTrusted) {\n            msalInstance.handlePopupPromise().catch((error)=>{\n                console.error(\"Error handling popup:\", error);\n            });\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_4___default().useEffect(()=>{\n        window.addEventListener(_azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__.EventType.LOGIN_SUCCESS, handlePopup);\n        window.addEventListener(_azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__.EventType.ACQUIRE_TOKEN_SUCCESS, handlePopup);\n        return ()=>{\n            window.removeEventListener(_azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__.EventType.LOGIN_SUCCESS, handlePopup);\n            window.removeEventListener(_azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__.EventType.ACQUIRE_TOKEN_SUCCESS, handlePopup);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_azure_msal_react__WEBPACK_IMPORTED_MODULE_2__.MsalProvider, {\n        instance: msalInstance,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\utils\\\\auth\\\\msalProvider.jsx\",\n        lineNumber: 27,\n        columnNumber: 10\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MsalAuthProvider);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/auth/msalProvider.jsx\n");

/***/ }),

/***/ "./utils/loaders/loadingContext.js":
/*!*****************************************!*\
  !*** ./utils/loaders/loadingContext.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingProvider: () => (/* binding */ LoadingProvider),\n/* harmony export */   useLoading: () => (/* binding */ useLoading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n// context/LoadingContext.js\n\n\nconst LoadingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst LoadingProvider = ({ children })=>{\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingContext.Provider, {\n        value: {\n            isLoading,\n            setIsLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\utils\\\\loaders\\\\loadingContext.js\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\nconst useLoading = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LoadingContext);\n    if (!context) {\n        throw new Error(\"useLoading must be used within a LoadingProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9sb2FkZXJzL2xvYWRpbmdDb250ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFBLDRCQUE0Qjs7QUFDdUM7QUFFbkUsTUFBTUksK0JBQWlCSCxvREFBYUE7QUFFN0IsTUFBTUksa0JBQWtCLENBQUMsRUFBRUMsUUFBUSxFQUFFO0lBQzFDLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHTCwrQ0FBUUEsQ0FBQztJQUUzQyxxQkFDRSw4REFBQ0MsZUFBZUssUUFBUTtRQUFDQyxPQUFPO1lBQUVIO1lBQVdDO1FBQWE7a0JBQ3ZERjs7Ozs7O0FBR1AsRUFBRTtBQUVLLE1BQU1LLGFBQWE7SUFDeEIsTUFBTUMsVUFBVVYsaURBQVVBLENBQUNFO0lBQzNCLElBQUksQ0FBQ1EsU0FBUztRQUNaLE1BQU0sSUFBSUMsTUFBTTtJQUNsQjtJQUNBLE9BQU9EO0FBQ1QsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL3RobHdlYmFwcC8uL3V0aWxzL2xvYWRlcnMvbG9hZGluZ0NvbnRleHQuanM/OTA5ZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBjb250ZXh0L0xvYWRpbmdDb250ZXh0LmpzXHJcbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xyXG5cclxuY29uc3QgTG9hZGluZ0NvbnRleHQgPSBjcmVhdGVDb250ZXh0KCk7XHJcblxyXG5leHBvcnQgY29uc3QgTG9hZGluZ1Byb3ZpZGVyID0gKHsgY2hpbGRyZW4gfSkgPT4ge1xyXG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8TG9hZGluZ0NvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3sgaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmcgfX0+XHJcbiAgICAgIHtjaGlsZHJlbn1cclxuICAgIDwvTG9hZGluZ0NvbnRleHQuUHJvdmlkZXI+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCB1c2VMb2FkaW5nID0gKCkgPT4ge1xyXG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KExvYWRpbmdDb250ZXh0KTtcclxuICBpZiAoIWNvbnRleHQpIHtcclxuICAgIHRocm93IG5ldyBFcnJvcihcInVzZUxvYWRpbmcgbXVzdCBiZSB1c2VkIHdpdGhpbiBhIExvYWRpbmdQcm92aWRlclwiKTtcclxuICB9XHJcbiAgcmV0dXJuIGNvbnRleHQ7XHJcbn07XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlU3RhdGUiLCJMb2FkaW5nQ29udGV4dCIsIkxvYWRpbmdQcm92aWRlciIsImNoaWxkcmVuIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiUHJvdmlkZXIiLCJ2YWx1ZSIsInVzZUxvYWRpbmciLCJjb250ZXh0IiwiRXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./utils/loaders/loadingContext.js\n");

/***/ }),

/***/ "./utils/loaders/overlaySpinner.js":
/*!*****************************************!*\
  !*** ./utils/loaders/overlaySpinner.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-loader-spinner */ \"react-loader-spinner\");\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_loader_spinner__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _loadingContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./loadingContext */ \"./utils/loaders/loadingContext.js\");\n// components/OverlaySpinner.js\n\n\n\n\nconst OverlaySpinner = ()=>{\n    const { isLoading } = (0,_loadingContext__WEBPACK_IMPORTED_MODULE_3__.useLoading)();\n    return isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            height: \"100vh\",\n            width: \"100vw\",\n            position: \"fixed\",\n            backgroundColor: \"white\",\n            zIndex: 999999\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_loader_spinner__WEBPACK_IMPORTED_MODULE_2__.ThreeCircles, {\n            color: \"#002D73\",\n            height: 50,\n            width: 50,\n            visible: isLoading,\n            ariaLabel: \"oval-loading\",\n            secondaryColor: \"#0066FF\",\n            strokeWidth: 2,\n            strokeWidthSecondary: 2\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\utils\\\\loaders\\\\overlaySpinner.js\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\utils\\\\loaders\\\\overlaySpinner.js\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OverlaySpinner);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/loaders/overlaySpinner.js\n");

/***/ }),

/***/ "./utils/rolePermissionsContext.js":
/*!*****************************************!*\
  !*** ./utils/rolePermissionsContext.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PermissionsProvider: () => (/* binding */ PermissionsProvider),\n/* harmony export */   usePermissions: () => (/* binding */ usePermissions)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst PermissionsContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst PermissionsProvider = ({ children })=>{\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const updatePermissions = (newPermissions)=>{\n        setPermissions(newPermissions);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PermissionsContext.Provider, {\n        value: {\n            permissions,\n            updatePermissions\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\utils\\\\rolePermissionsContext.js\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, undefined);\n};\nconst usePermissions = ()=>{\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(PermissionsContext);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9yb2xlUGVybWlzc2lvbnNDb250ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBNEQ7QUFFNUQsTUFBTUcsbUNBQXFCSCxvREFBYUE7QUFFakMsTUFBTUksc0JBQXNCLENBQUMsRUFBRUMsUUFBUSxFQUFFO0lBQzlDLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHTCwrQ0FBUUEsQ0FBQyxDQUFDO0lBRWhELE1BQU1NLG9CQUFvQixDQUFDQztRQUN6QkYsZUFBZUU7SUFDakI7SUFFQSxxQkFDRSw4REFBQ04sbUJBQW1CTyxRQUFRO1FBQUNDLE9BQU87WUFBRUw7WUFBYUU7UUFBa0I7a0JBQ2xFSDs7Ozs7O0FBR1AsRUFBRTtBQUVLLE1BQU1PLGlCQUFpQjtJQUM1QixPQUFPWCxpREFBVUEsQ0FBQ0U7QUFDcEIsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL3RobHdlYmFwcC8uL3V0aWxzL3JvbGVQZXJtaXNzaW9uc0NvbnRleHQuanM/ZjdhYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcclxuXHJcbmNvbnN0IFBlcm1pc3Npb25zQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQoKTtcclxuXHJcbmV4cG9ydCBjb25zdCBQZXJtaXNzaW9uc1Byb3ZpZGVyID0gKHsgY2hpbGRyZW4gfSkgPT4ge1xyXG4gIGNvbnN0IFtwZXJtaXNzaW9ucywgc2V0UGVybWlzc2lvbnNdID0gdXNlU3RhdGUoe30pO1xyXG5cclxuICBjb25zdCB1cGRhdGVQZXJtaXNzaW9ucyA9IChuZXdQZXJtaXNzaW9ucykgPT4ge1xyXG4gICAgc2V0UGVybWlzc2lvbnMobmV3UGVybWlzc2lvbnMpO1xyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8UGVybWlzc2lvbnNDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt7IHBlcm1pc3Npb25zLCB1cGRhdGVQZXJtaXNzaW9ucyB9fT5cclxuICAgICAge2NoaWxkcmVufVxyXG4gICAgPC9QZXJtaXNzaW9uc0NvbnRleHQuUHJvdmlkZXI+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCB1c2VQZXJtaXNzaW9ucyA9ICgpID0+IHtcclxuICByZXR1cm4gdXNlQ29udGV4dChQZXJtaXNzaW9uc0NvbnRleHQpO1xyXG59O1xyXG4iXSwibmFtZXMiOlsiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJ1c2VTdGF0ZSIsIlBlcm1pc3Npb25zQ29udGV4dCIsIlBlcm1pc3Npb25zUHJvdmlkZXIiLCJjaGlsZHJlbiIsInBlcm1pc3Npb25zIiwic2V0UGVybWlzc2lvbnMiLCJ1cGRhdGVQZXJtaXNzaW9ucyIsIm5ld1Blcm1pc3Npb25zIiwiUHJvdmlkZXIiLCJ2YWx1ZSIsInVzZVBlcm1pc3Npb25zIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./utils/rolePermissionsContext.js\n");

/***/ }),

/***/ "./utils/secureThemeContext.js":
/*!*************************************!*\
  !*** ./utils/secureThemeContext.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SecureThemeProvider: () => (/* binding */ SecureThemeProvider),\n/* harmony export */   useSecureTheme: () => (/* binding */ useSecureTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst SecureThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst SecureThemeProvider = ({ children, initialTheme = \"#022D71\" })=>{\n    const [themeColor, setThemeColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialTheme);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Initialize theme from session data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeTheme = async ()=>{\n            try {\n                const apiBase = \"http://localhost:8081\" || 0;\n                // Get theme from session via API call\n                const response = await fetch(`${apiBase}/api/auth/me`, {\n                    method: \"GET\",\n                    credentials: \"include\"\n                });\n                if (response.ok) {\n                    const { user } = await response.json();\n                    if (user?.theme) {\n                        setThemeColor(user.theme);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error loading theme:\", error);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        initializeTheme();\n    }, []);\n    // Apply theme to CSS variables\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (false) {}\n    }, [\n        themeColor\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SecureThemeContext.Provider, {\n        value: {\n            themeColor,\n            setThemeColor,\n            isLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\utils\\\\secureThemeContext.js\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, undefined);\n};\nconst useSecureTheme = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SecureThemeContext);\n    if (!context) {\n        throw new Error(\"useSecureTheme must be used within a SecureThemeProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/secureThemeContext.js\n");

/***/ }),

/***/ "./utils/serviceCustomerContext.js":
/*!*****************************************!*\
  !*** ./utils/serviceCustomerContext.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ServiceCustomerProvider: () => (/* binding */ ServiceCustomerProvider),\n/* harmony export */   useServiceCustomers: () => (/* binding */ useServiceCustomers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst ServiceCustomersContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst ServiceCustomerProvider = ({ children })=>{\n    const [serviceCustomers, setServiceCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        value: \"All\",\n        label: \"All Service Customers\"\n    });\n    const updateServiceCustomersList = (newServiceCustomersList)=>{\n        setServiceCustomers(newServiceCustomersList);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ServiceCustomersContext.Provider, {\n        value: {\n            serviceCustomers,\n            updateServiceCustomersList\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\utils\\\\serviceCustomerContext.js\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, undefined);\n};\nconst useServiceCustomers = ()=>{\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ServiceCustomersContext);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/serviceCustomerContext.js\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-loader-spinner":
/*!***************************************!*\
  !*** external "react-loader-spinner" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-loader-spinner");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "@azure/msal-browser":
/*!**************************************!*\
  !*** external "@azure/msal-browser" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@azure/msal-browser");;

/***/ }),

/***/ "@azure/msal-react":
/*!************************************!*\
  !*** external "@azure/msal-react" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@azure/msal-react");;

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();