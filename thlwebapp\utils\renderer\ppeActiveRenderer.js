import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faCopy,
  faPenToSquare,
  faXmark,
  faInfo,
  faFileExport,
  faEye,
  faToggleOn,
  faToggleOff
} from "@fortawesome/free-solid-svg-icons";
import { Router, useRouter } from "next/router";
import { apiConfig } from "@/services/apiConfig";
import { useState, useEffect, Fragment } from "react";
import * as XLSX from "xlsx";
import exportExcelData from "../exportExcel";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { Dialog, Transition } from "@headlessui/react";
//
import { useMsal } from "@azure/msal-react";
import Cookies from "js-cookie";
import { logout } from "../secureStorage";

const productActiveRenderer = (
  params, 
  userData,
  company,
  typeId,
  setIsLoading,
  isIssUser,
  isIssProcurementTeamUser,
  isIssAdmin,
  superAdmin
) => {

  const router = useRouter();
  // console.log("------------------",params)
  const product_id = params.data.product_id;
  const data = params.data;
  const serverAddress = apiConfig.serverAddress;
  const [isCancelOpen, setIsCancelOpen] = useState(false);
  const [cancelledReasonapi, setCancelledReasonapi] = useState("");
  const [isValidCancelReason, setIsValidCancelReason] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const editProduct = () => {
    setIsEditing(true);
    setIsLoading(true);
    if (typeof window !== "undefined") {
      if (params && params?.data?.type == "FG") {
        router.push({
          pathname: `/finished-product-request/${product_id}/edit`,
        });
      } else if (params && params.data.type == "RM") {
        setIsLoading(true);

        router.push({
          pathname: `/raw-material-request/${product_id}/edit`,
        });
      } else if (params && params.data.type == "NV") {
        setIsLoading(true);

        router.push({
          pathname: `/variety/${product_id}/edit`,
        });
      } else if (params && params.data.type == "PK"){
        setIsLoading(true);
        router.push({
          pathname: `/packaging-form/${product_id}/edit`
        })
      }
    }
  };

  const handleCancelReason = (data) => {
    if (data) {
      setIsValidCancelReason(true);
    } else {
      setIsValidCancelReason(false);
    }
  };
  const trimInputText = (input) => {
    return input.trim();
  };

  const hideProduct = () => {
    setIsCancelOpen(true);
  };

  const closeCancelModal = () => {
    setIsCancelOpen(false);
  };

  const getProphetId = () => {
    switch (params.data.company) {
      case "dpsltd":
        return 1;
      case "efcltd":
        return 3;
      case "fpp-ltd":
        return 4;
      default:
        return 1;
    }
  };

  const saveModalData = (markHidden) => {
    const prophetId = getProphetId();
    // console.log("begin--------------------------")
    try {
      fetch(`${serverAddress}ppe-consumables/product-update-hidden-state`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          productId: product_id,
          markHidden: markHidden,
          cancelled_by: userData.email,
          cancelled_by_name: userData.name,
        }),
      })
        .then((res) => {
          if (res.status === 400) {
            toast.error(
              "There was an error with your request. Please check your data and try again."
            );
          }
          if (res.status === 200) {
            return res.json();
          } else if (res.status === 401) {
            toast.error("Your session has expired. Please log in again.");
            setTimeout(async() => {
              await logout();
              const redirectUrl = `/login?redirect=${encodeURIComponent(
                window.location.pathname
              )}`;
              router.push(redirectUrl);
            }, 3000);
          }
          return null;
        })
        .then((json) => {
        if (json.success) {
          toast.success("Product de-activated sucessfully", {
            postition: "top-right",
          });
          router.reload()
        } else {
          toast.error("Cannot de-activate as product already in use");
        }
        setIsCancelOpen(false);
      });
        // console.log("end------------------")
    } catch (error) {
      console.error("Failed to hide product by :", error);
    }
  };
  return (
    <>
      {/* <ToastContainer limit={1} /> */}
      <div className="flex flex-row gap-4 justify-start text-blue-500 pl-3">
        {params.data.status == "Prophet Setup Completed" ||
        params.data.status == "Prophet to Setup" ||
        params.data.status == "Cancelled" ? (
          <button title="View Request" onClick={editProduct}>
            <FontAwesomeIcon
              icon={faEye}
              size="lg"
              className="text-skin-primary"
            />
          </button>
        ) : (
          <>
          <button
            title={params.data.IsProductHidden ?"In-Active" : "Active"}
          
          >
            <FontAwesomeIcon
                icon={params.data.IsProductHidden ? faToggleOff : faToggleOn }
                size="xl"
                className="text-skin-primary"
                onClick={hideProduct}
              />
          </button>
          </>
        )}
      </div>

      <Transition appear show={isCancelOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={closeCancelModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex items-center justify-center min-h-full p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className=" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all">
                  {/* <!-- Modal content --> */}
                  <div className="relative bg-white rounded-lg shadow">
                    {/* <!-- Modal header --> */}
                    <div className="flex items-start justify-between p-8 rounded-t">
                      <h3 className="flex flex-row text-xl font-semibold text-gray-900  items-center">
                        <span className="flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4">
                          <FontAwesomeIcon icon={faInfo} />{" "}
                        </span>{" "}
                        Do you want to hide the selected Product? 
                      </h3>
                      <button
                        onClick={closeCancelModal}
                        type="button"
                        className="text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center "
                        data-modal-hide="default-modal"
                      >
                        <FontAwesomeIcon
                          icon={faXmark}
                          className="text-skin-primary"
                        />{" "}
                      </button>
                    </div>
                    {/* <!-- Modal body --> */}
                    <div className="p-8 py-0 space-y-6">
                        <p className="text-base xl:text-md 2xl:text-lg leading-relaxed mt-2">
                          Do you want to make the product {params.data.IsProductHidden ? "in" : ""}active?
                        </p>
                    </div>
                    {/* <!-- Modal footer --> */}
                    <div className="flex items-end p-6 space-x-2 justify-end">
                      <button
                        onClick={() => saveModalData(params.data.IsProductHidden ? 0 : 1)}
                        data-modal-hide="default-modal"
                        type="button"
                        className=" bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md text-white mr-1 px-6 py-2 text-center "
                      >
                        {params.data.IsProductHidden ? "Show" : "Hide"} Product
                      </button>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
};

export default productActiveRenderer;
