BEGIN TRY
    BEGIN TRANSACTION;

    -- A. Update main product details
    UPDATE [Iss_ppe_consumables_portal].[dbo].[products]
    SET 
        [name] = @ProductName,
        [type_id] = @TypeID,
        [size_required] = @IsSizeRequired,
        [name_printable] = @NamePrintable
    WHERE [id] = @ProductID;

    -- B. Handle sizes if required
    IF (@IsSizeRequired = 1)
    BEGIN
        -- If ordered JSON data is provided
        IF (@SizesWithOrderJSON IS NOT NULL AND @SizesWithOrderJSON != '')
        BEGIN
            ;WITH OrderedSizes AS (
                SELECT 
                    JSON_VALUE(value, '$.name') AS size_label,
                    CAST(JSON_VALUE(value, '$.displayOrder') AS INT) AS display_order
                FROM OPENJSON(@SizesWithOrderJSON)
                WHERE JSON_VALUE(value, '$.name') IS NOT NULL
                  AND LTRIM(RTRIM(JSON_VALUE(value, '$.name'))) != ''
            )
            -- 1) Insert missing sizes into master size table
            INSERT INTO [Iss_ppe_consumables_portal].[dbo].[product_sizes] (size_label, is_active, created_at)
            SELECT DISTINCT os.size_label, 1, GETDATE()
            FROM OrderedSizes os
            LEFT JOIN [Iss_ppe_consumables_portal].[dbo].[product_sizes] ps
                ON ps.size_label = os.size_label
            WHERE ps.id IS NULL;

            -- 2) Deactivate all current links (soft delete approach)
            UPDATE pas
            SET pas.is_active = 0
            FROM [Iss_ppe_consumables_portal].[dbo].[product_available_sizes] pas
            WHERE pas.product_id = @ProductID;

            -- 3) Reactivate or insert requested sizes with correct display order
            ;WITH OrderedSizes AS (
                SELECT 
                    JSON_VALUE(value, '$.name') AS size_label,
                    CAST(JSON_VALUE(value, '$.displayOrder') AS INT) AS display_order
                FROM OPENJSON(@SizesWithOrderJSON)
            )
            MERGE [Iss_ppe_consumables_portal].[dbo].[product_available_sizes] AS target
            USING (
                SELECT 
                    @ProductID AS product_id,
                    ps.id AS size_id,
                    os.display_order
                FROM OrderedSizes os
                JOIN [Iss_ppe_consumables_portal].[dbo].[product_sizes] ps
                    ON ps.size_label = os.size_label
            ) AS source
            ON target.product_id = source.product_id
               AND target.size_id = source.size_id
            WHEN MATCHED THEN
                UPDATE SET 
                    target.display_order = source.display_order,
                    target.is_active = 1
            WHEN NOT MATCHED BY TARGET THEN
                INSERT (product_id, size_id, display_order, is_active)
                VALUES (source.product_id, source.size_id, source.display_order, 1);
        END
        ELSE IF (@NewSizeNames IS NOT NULL AND @NewSizeNames != '')
        BEGIN
            ;WITH NewSizes AS (
                SELECT DISTINCT
                    NULLIF(LTRIM(RTRIM(value)), '') AS size_label,
                    ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) AS display_order
                FROM STRING_SPLIT(@NewSizeNames, ',')
                WHERE NULLIF(LTRIM(RTRIM(value)), '') IS NOT NULL
            )
            INSERT INTO [Iss_ppe_consumables_portal].[dbo].[product_sizes] (size_label, is_active, created_at)
            SELECT ns.size_label, 1, GETDATE()
            FROM NewSizes ns
            LEFT JOIN [Iss_ppe_consumables_portal].[dbo].[product_sizes] ps
                ON ps.size_label = ns.size_label
            WHERE ps.id IS NULL;

            -- deactivate existing
            UPDATE pas
            SET pas.is_active = 0
            FROM [Iss_ppe_consumables_portal].[dbo].[product_available_sizes] pas
            WHERE pas.product_id = @ProductID;

            -- reactivate/insert
            ;WITH NewSizes AS (
                SELECT DISTINCT
                    NULLIF(LTRIM(RTRIM(value)), '') AS size_label,
                    ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) AS display_order
                FROM STRING_SPLIT(@NewSizeNames, ',')
                WHERE NULLIF(LTRIM(RTRIM(value)), '') IS NOT NULL
            )
            MERGE [Iss_ppe_consumables_portal].[dbo].[product_available_sizes] AS target
            USING (
                SELECT 
                    @ProductID AS product_id,
                    ps.id AS size_id,
                    ns.display_order
                FROM NewSizes ns
                JOIN [Iss_ppe_consumables_portal].[dbo].[product_sizes] ps
                    ON ps.size_label = ns.size_label
            ) AS source
            ON target.product_id = source.product_id
               AND target.size_id = source.size_id
            WHEN MATCHED THEN
                UPDATE SET 
                    target.display_order = source.display_order,
                    target.is_active = 1
            WHEN NOT MATCHED BY TARGET THEN
                INSERT (product_id, size_id, display_order, is_active)
                VALUES (source.product_id, source.size_id, source.display_order, 1);
        END
    END

    COMMIT TRANSACTION;

    SELECT @ProductID AS UpdatedProductID, 'Success' AS Status;
END TRY
BEGIN CATCH
    IF @@TRANCOUNT > 0
        ROLLBACK TRANSACTION;
    SELECT -1 AS UpdatedProductID, 'Failed: ' + ERROR_MESSAGE() AS Status;
END CATCH;
