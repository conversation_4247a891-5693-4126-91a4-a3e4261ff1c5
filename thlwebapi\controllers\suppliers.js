"use strict";

const { map } = require("mssql");
const supplierData = require("../data/suppliers");
const logger = require("../utils/logger");

const getSuppliers = async (req, res, next) => {
  try {
    const { company, prophetId } = req.params;
    const suppliers = await supplierData.getSuppliers(company, prophetId);
    res.status(200).json(suppliers);
  } catch (error) {
    logger.error({
      username: req.session?.user?.name,
      type: "error", 
      description: `Error fetching suppliers: ${error.message}`,
    });
    res.status(400).send(error.message);
  }
};

const getLinkedSuppliers = async (req, res, next) => {
  try {
    const company = req?.params?.company;
    // Remove verifyToken check - session validation handled by middleware
    const linkedSupplierId = req.params?.linkedSup;

    const suppliers = await supplierData.getLinkedSuppliers(
      linkedSupplierId,
      req?.session?.user?.name,
      company
    );
    res.send(suppliers);
  } catch (error) {
    console.error("Error", error);
    logger.error({
      username: req?.session?.user?.name,
      type: "error",
      description: `Error fetching Suppliers for user with email ${req?.session?.user?.email}`,
    });
    res.status(400).send(error.message);
  }
};

const getSupplierRoles = async (req, res, next) => {
  //*modified fetch status
  try {
    // Remove verifyToken check - session validation handled by middleware
    const supplierRoles = await supplierData.getSupplierRoles(
      req?.session?.user?.name
    );
    if (supplierRoles && supplierRoles?.length > 0) {
      res.send(supplierRoles);
    } else {
      res.status(404).send("Failed to fetch supplier roles");
    }
  } catch (error) {
    console.error("Error", error);
    res.status(400).send(error.message);
  }
};
const getSupplierTypes = async (req, res, next) => {
  //*modified fetch status
  try {
    // Remove verifyToken check - session validation handled by middleware
    const supplierTypes = await supplierData.getSupplierTypes(
      req?.session?.user?.name
    );
    if (supplierTypes && supplierTypes?.length > 0) {
      res.send(supplierTypes);
    } else {
      res.status(404).send("Failed to fetch supplier types");
    }
  } catch (error) {
    console.error("Error", error);
    res.status(400).send(error.message);
  }
};

const getProductByProphet = async (req, res, next) => {
  //!products controller
  try {
    const prophet_id = req?.params?.prophet_id;
    // Remove verifyToken check - session validation handled by middleware
    const supplierProphets = await supplierData.getProductByProphet(
      prophet_id
    );
    res.send(supplierProphets);
  } catch (error) {
    console.error("Error", error);
    res.status(400).send(error.message);
  }
};

const getRolePermissions = async (req, res, next) => {
  //*modified fetch status
  try {
    // Remove verifyToken check - session validation handled by middleware
    const rolePermissions = await supplierData.getRolePermissions(
      req?.session?.user?.name
    );

    if (rolePermissions && rolePermissions?.length > 0) {
      res.send(rolePermissions);
    } else {
      res.status(404).send("Failed to fetch roles permissions");
    }
  } catch (error) {
    console.error("Error", error);
    res.status(400).send(error.message);
  }
};

const getLinksByProphets = async (req, res, next) => {
  //!not sure if used
  try {
    // Remove verifyToken check - session validation handled by middleware
    const prophetId = req?.params?.prophetId;
    const supplierLinks = await supplierData.getLinksByProphets(
      prophetId,
      req?.session?.user?.name
    );

    res.send(supplierLinks);
  } catch (error) {
    console.error("Error", error);
    res.status(400).send(error.message);
  }
};

//*modified fetch status below
const getSupplierLinks = async (req, res, next) => {
  //!this is called but not sure if its used
  //*modified fetch status
  try {
    const company = req?.params?.company;
    // Remove verifyToken check - session validation handled by middleware
    const rolesString = req?.params?.roles;
    const supplierLinks = await supplierData.getSupplierLinks(
      req?.session?.user?.name,
      company
    );
    if (supplierLinks && supplierLinks?.length > 0) {
      res.send(supplierLinks);
    } else {
      res.status(404).send("Failed to fetch supplier links");
    }
  } catch (error) {
    console.error("Error", error);
    res.status(400).send(error.message);
  }
};

const getSupplierById = async (req, res, next) => {
  //*modified fetch status
  try {
    // Remove verifyToken check - session validation handled by middleware
    const supplierId = req?.params?.id;
    const supplier = await supplierData.getSupplierById(
      supplierId,
      req?.session?.user?.name
    );

    if (supplier && supplier?.length > 0) {
      res.send(supplier);
    } else {
      res.status(404).send("No data found");
    }
  } catch (error) {
    console.error("Error", error);
    res.status(400).send(error.message);
  }
};

const createSupplier = async (req, res, next) => {
  try {
    // Remove verifyToken check - session validation handled by middleware
    const email = req?.session?.user?.email;
    const name = req?.session?.user?.name;
    const data = req?.body;
    const created = await supplierData.createSupplier(data, name, email);

    if (created && created?.status == 201) {
      res.status(201).json({
        message: "Supplier created successfully",
        data: created.data,
      });
    } else {
      res.status(400).send("Failed to create supplier");
    }
  } catch (error) {
    console.error("Error", error);
    logger.error({
      username: req?.session?.user?.name,
      type: "error",
      description: `Error creating supplier: ${error.message}`,
    });
    res.status(400).send(error.message);
  }
};

const addSendacGroup = async (req, res, next) => {
  //!not sure if this is used
  try {
    // Remove verifyToken check - session validation handled by middleware
    const data = req.body;

    const created = await supplierData.createSendacGroup(
      data,
      req?.session?.user?.name
    );

    res.send(created);
  } catch (error) {
    console.error("Error", error);
    logger.error({
      username: req?.session?.user?.name,
      type: "error",
      description: `Error adding sendac group for user with email ${req?.session?.user?.email} with message: ${error.message}`,module_id: 1,
    });
    res.status(400).send(error.message);
  }
};

const getLinksBySendac = async (req, res, next) => {
  //!mostly not used
  const email = req?.session?.user?.email;
  const name = req?.session?.user?.name;
  try {
    // Remove verifyToken check - session validation handled by middleware
    const sendac_id = req?.params?.sendacId;
    const links = await supplierData.getSendacLinksBySendac(
      sendac_id,
      email,
      name
    );

    res.send(links);
  } catch (error) {
    console.error("Error", error);
    logger.error({
      username: name,
      type: "error",
      description: `Error fetch links by sendac group for user with email ${email} with message: ${error.message}`,module_id: 1
    });
    res.status(400).send(error.message);
  }
};

const getSendacGroupByProphets = async (req, res, next) => {
  const prophets = req.body;
  try {
    // Remove verifyToken check - session validation handled by middleware
    const sendac_group = await supplierData.getSendacGroupByProphets(
      prophets
    );
    res.send(sendac_group);
  } catch (error) {
    console.error("Error", error);
    res.status(400).send(error.message);
  }
};

const getFilteredSupplierNames = async (req, res, next) => {
  try {
    // Remove verifyToken check - session validation handled by middleware
    const searchString = req?.params?.searchString;
    const supplierNames = await supplierData.getFilteredSupplierNames(
      searchString
    );
    res.send(supplierNames);
  } catch (error) {
    console.error("Error", error);
    res.status(400).send(error.message);
  }
};
const getIfUniqueSupplierCode = async (req, res, next) => {
  try {
    // Remove verifyToken check - session validation handled by middleware
    const searchString = req?.params?.searchString;
    const supplierId = req?.params?.supplierId;
    const isUnique = await supplierData.getIfUniqueSupplierCode(
      searchString,
      supplierId
    );

    res.send(isUnique);
  } catch (error) {
    console.error("Error", error);
    res.status(400).send(error.message);
  }
};

const getFilteredDistribution = async (req, res, next) => {
  try {
    // Remove verifyToken check - session validation handled by middleware
    const searchString = req?.params?.searchString;
    const prophet = req?.body?.prophetsIds;
    const supplierId = req?.body?.supplierId;
    const supplierNames = await supplierData.getFilteredDistribution(
      searchString,
      prophet,
      supplierId
    );
    res.send(supplierNames);
  } catch (error) {
    console.error("Error", error);
    res.status(400).send(error.message);
  }
};
const checkDistributions = async (req, res, next) => {
  try {
    // Remove verifyToken check - session validation handled by middleware
    const distributionPoint = req?.body?.newItem?.distributionPoint;
    const prophet = req?.body?.prophetId;
    const supplierId = req?.body?.supplierId;
    const supplierNames = await supplierData.checkDistribution(
      prophet,
      supplierId,
      distributionPoint
    );
    if (
      supplierNames[0]?.name?.toLowerCase() ===
        distributionPoint?.toLowerCase() &&
      supplierNames[0]?.from_dp === null
    ) {
      res.status(201).json({ exist: true });
    } else if (
      supplierNames[0]?.name?.toLowerCase() ===
        distributionPoint?.toLowerCase() &&
      supplierNames[0]?.from_dp !== null
    ) {
      res
        .status(201)
        .json({ exist: false, isOld: true, data: supplierNames });
    } else {
      res.status(201).json({ exist: false, isOld: false });
    }
  } catch (error) {
    console.error("Error", error);
    res.status(400).send(error.message);
  }
};

const updateSupplier = async (req, res, next) => {
  //*modified fetch status

  try {
    // Remove verifyToken check - session validation handled by middleware
    const referer = req.get("Referer");
    const url = new URL(referer);
    const frontendDomain = url.origin;
    const email = req?.session?.user?.email;
    const name = req?.session?.user?.name;
    const supplierId = req?.params?.id;
    const data = req?.body;
    const sectionName = req.body?.sectionName;
    const deleteFlags = req.body?.deleteFlags;
    const status = "";
    let result;
    if (sectionName == "financialsSection") {
      result = await supplierData.updateFinancialData(
        //*modfied
        supplierId,
        data,
        email,
        name,
        frontendDomain
      );
    } else if (sectionName == "generalSection") {
      result = await supplierData.updateGeneralData(
        //*modfied
        supplierId,
        data,
        email,
        name
      );
    } else if (sectionName == "procurementSection") {
      //*modfied
      result = await supplierData.updateDTAYData(
        supplierId,
        data,
        email,
        name
      );
    } else if (sectionName == "complianceSection") {
      //*modfied
      const updatedComplianceSection =
        await supplierData.updateComplianceData(
          supplierId,
          data,
          email,
          name
        );

      if (
        updatedComplianceSection &&
        updatedComplianceSection?.hasExistingItems
      ) {
        res.status(409).json({ msg: updatedComplianceSection?.items });
        return;
      } else if (updatedComplianceSection.status == 400) {
        res.status(400).send("Failed to update supplier: " + error.message);
        return;
      } else {
        res
          .status(200)
          .send({ status: 200, message: "Supplier successfully updated" });
        return;
      }
    } else if (sectionName == "updateSupplier") {
      result = await supplierData.updateSupplier(
        //*modified
        supplierId,
        data,
        email,
        name
      );
    } else if (sectionName == "sendacOperation") {
      result = await supplierData.moveLinks(
        //*modified
        supplierId,
        data,
        name,
        email
      );
    } else if (sectionName == "removeSendacOperation") {
      result = await supplierData.removeLinks(
        //*modified
        supplierId,
        data,
        name,
        email
      );
    } else if (sectionName == "updateStatus") {
      result = await supplierData.updateStatus(
        //*modified
        supplierId,
        data,
        email,
        name
      );
      if (req.body.exported) {
        logger.info({
          username: req?.session?.user?.name,
          type: "success",
          description: `User with email ${req?.session?.user?.email} did an ${req?.body?.to} export for supplier ${req?.body?.company_name}`,
          item_id: supplierId,
          module_id:1
        });
      }
    }
    //*Added for
    //*general section
    //*financial section
    //*compliance section
    //*dtay section
    //*updateSupplier
    //*movelinks
    //*removelinks

    if (result.status === 200) {
      res
        .status(200)
        .send({ status: 200, message: "Supplier successfully updated" });
    } else {
      res.status(400).send("Failed to update supplier");
    }
  } catch (error) {
    console.error("Error", error);
    logger.error({
      username: req?.session?.user?.name,
      type: "error",
      description: error.message,
      item_id: req?.params?.id,module_id: 1
    });
    res.status(400).send("Failed to update supplier: " + error.message);
  }
};

const deleteSupplier = async (req, res, next) => {
  try {
    // Remove verifyToken check - session validation handled by middleware
    const supplierId = req?.params?.id;
    const deletedSupplier = await supplierData.deleteSupplier(
      supplierId,
      req?.session?.user?.name
    );
    res.send(deletedSupplier);
  } catch (error) {
    console.error("Error", error);
    res.status(400).send(error.message);
  }
};

const checkProphetCode = async (req, res, next) => {
  try {
    // Remove verifyToken check - session validation handled by middleware
    const prophetData = req?.body;
    const checkProphetCode = await supplierData.checkExistingProphetCode(
      prophetData,
      req?.session?.user?.name
    );
    res.send(checkProphetCode);
  } catch (error) {
    console.error("Error", error);
    logger.error({
      username: req?.session?.user?.name,
      type: "error",
      description: `Failed prophet code check for user ${req?.session?.user?.email} with message: ${error.message}`,
      item_id: req?.body[0]?.supplier_id,module_id: 1
    });
    res.status(400).send(error.message);
  }
};

const insertProphetCode = async (req, res, next) => {
  try {
    // Remove verifyToken check - session validation handled by middleware
    const prophetData = req.body;
    const prophetCodes = await supplierData.insertProphetCode(
      prophetData,
      req?.session?.user?.name
    );

    res.send(prophetCodes);
  } catch (error) {
    console.error("Error", error);
    logger.error({
      username: req?.session?.user?.name,
      type: "error",
      description: `Failed adding prophet code for user ${req?.session?.user?.email} with message: ${error.message}`,
      item_id: req?.body[0]?.supplier_id,module_id: 1
    });
    res.status(400).send(error.message);
  }
};
const deleteAll = async (req, res, next) => {
  try {
    // Remove verifyToken check - session validation handled by middleware
    const result = await supplierData.deleteAll();
    res.status(200).send(result);
  } catch (error) {
    console.error("Error", error);
    logger.error({
      username: req?.session?.user?.name,
      type: "error",
      description: `Failed adding prophet code for user ${req?.session?.user?.email} with message: ${error.message}`,
      item_id: req?.body[0]?.supplier_id,module_id: 1
    });
    res.status(400).send(error.message);
  }
};
const getSuppliersExtractData = async (req, res, next) => {
  try {
    // Remove verifyToken check - session validation handled by middleware
    const supplierId = req?.params?.id;
    const result = await supplierData.getSuppliersExtractData(supplierId);
    res.status(200).send(result);
  } catch (error) {
    console.error("Error", error);
    // logger.error({
    //   username: req?.user?.name,
    //   type: "error",
    //   description: `Failed adding prophet code for user ${req?.user?.email} with message: ${error.message}`,
    //   item_id: req?.body[0]?.supplier_id,module_id: 1
    // });
    res.status(400).send(error.message);
  }
};

const getAllDropdownList = async (req, res, next) => {
  const dropDownList = req?.body?.allDropDowns;
  const prophets = req?.body?.prophets;

  const data = {
    countries: null,
    currencies: null,
    country_codes: null,
    hauliers: null,
    delivery_terms: null,
    transports: null,
    type_of_contacts: null,
    payment_types: null,
    agreed_terms: null,
    brands: null,
    end_customers: null,
  };
  try {
    // Remove verifyToken check - session validation handled by middleware
    if (dropDownList?.includes("country")) {
      data.countries = await supplierData.getCountriesList(prophets);
    }
    if (dropDownList?.includes("currency")) {
      data.currencies = await supplierData.getCurrenciesList(prophets);
    }
    if (dropDownList?.includes("haulier")) {
      data.hauliers = await supplierData.getHauliersList();
    }
    if (dropDownList?.includes("delivery_term")) {
      data.delivery_terms = await supplierData.getDeliveryTermsList(prophets);
    }
    if (dropDownList?.includes("transport")) {
      data.transports = await supplierData.getTransportsList(prophets);
    }
    if (dropDownList?.includes("type_contact")) {
      data.type_of_contacts = await supplierData.getTypeOfContactsList();
    }
    if (dropDownList?.includes("payment_type")) {
      data.payment_types = await supplierData.getPaymentTypesList(prophets);
    }
    if (dropDownList?.includes("agreed_term")) {
      data.agreed_terms = await supplierData.getAgreedTermsList();
    }

    res.send(data);
  } catch (error) {
    console.error("Error", error);
    res.status(400).send(error.message);
  }
};

module.exports = {
  getSuppliers,
  getLinkedSuppliers,
  getRolePermissions,
  getSupplierRoles,getSupplierTypes,
  getSupplierLinks,
  getSupplierById,
  createSupplier,
  updateSupplier,
  deleteSupplier,
  addSendacGroup,
  getFilteredSupplierNames,
  getIfUniqueSupplierCode,
  getAllDropdownList,
  checkProphetCode,
  insertProphetCode,
  getLinksBySendac,
  getSendacGroupByProphets,
  getFilteredDistribution,
  checkDistributions,
  getLinksByProphets,
  getProductByProphet,
  deleteAll,getSuppliersExtractData
};
