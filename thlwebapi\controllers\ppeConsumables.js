"use strict";
const ppeConsumables = require("../data/ppeConsumables");
const logger = require("../utils/logger");

const allPpeRequests = async (req, res) => {
  try {
    const PpeRequests = await ppeConsumables.getOrderRequests();
    res.send(PpeRequests);
  } catch (error) {
    console.error("error fetching packaging requests by id", error);
    logger.error({
      username: req.session?.user?.name,
      type: "error",
      description: `Error fetching PPE requests: ${error.message}`,
    });
    res.status(400).send(error.message);
  }
};

const allProducts = async (req, res) => {
  try {
    const AllPpeRequests = req.params.id ? req.params.id : 55;
    const PpeRequests = await ppeConsumables.getProducts(AllPpeRequests);
    res.send(PpeRequests);
  } catch (error) {
    console.error("error fetching packaging requests by id", error);
    logger.error({
      username: req.session?.user?.name,
      type: "error",
      description: `Error fetching Products: ${error.message}`,
    });
    res.status(400).send(error.message);
  }
};

const getOrderRequestById = async (req, res) => {
  // console.log("req.params.id",req.params);
  try {
    const PpeRequestID = req.params.id;
    const PpeRequestData = await ppeConsumables.getRequestDataById(
      PpeRequestID
    );
    res.send(PpeRequestData);
  } catch (error) {
    console.error("error fetching packaging requests by id", error);
    logger.error({
      username: req.session?.user?.name,
      type: "error",
      description: `Error fetching order request by id: ${error.message}`,
    });
    res.status(400).send(error.message);
  }
};

const getProductRequestById = async (req, res) => {
  // console.log("req.params.id",req.params);
  try {
    const PpeRequestID = req.params.id;
    const PpeRequestData = await ppeConsumables.getProductRequestDataById(
      PpeRequestID
    );
    res.send(PpeRequestData);
  } catch (error) {
    console.error("error fetching packaging requests by id", error);
    logger.error({
      username: req.session?.user?.name,
      type: "error",
      description: `Error fetching product request by id: ${error.message}`,
    });
    res.status(400).send(error.message);
  }
};
const getProductType = async (req, res) => {
  // console.log("req.params.id",req.params);
  try {
    const PpeRequestData = await ppeConsumables.getProductType();
    res.send(PpeRequestData);
  } catch (error) {
    console.error("error fetching packaging requests by id", error);
    logger.error({
      username: req.session?.user?.name,
      type: "error",
      description: `Error fetching product type: ${error.message}`,
    });
    res.status(400).send(error.message);
  }
};

const sites = async (req, res) => {
  try {
    const SitesData = await ppeConsumables.getSites();
    res.send(SitesData);
  } catch (error) {
    console.error("error fetching site id's", error);
    logger.error({
      username: req.session?.user?.name,
      type: "error",
      description: `Error fetching sites: ${error.message}`,
    });
    res.status(400).send(error.message);
  }
};

const createOrderRequest = async (req, res) => {
  try {
    const result = await ppeConsumables.createOrderRequest(req.body);

    if (result.error) {
      return res.status(400).json({ error: result.error });
    }

    res.status(200).json({
      msg: "order added successfully",
      itemCount: result?.NewRequestID,
    });
  } catch (error) {
    console.error("Error adding product:", error);
    logger.error({
      username: req.session?.user?.name,
      type: "error",
      description: `Error creating order request: ${error.message}`,
    });
    res.status(400).send(error.message);
  }
};

async function updateOrderRequest(req, res) {
  try {
    // console.log("req in controller: ",req.body);
    const { requestId,action_id, submitterUserId, targetSiteId, requiredDate, items,comment } = req.body;

    // Basic validation
    if (
      !requestId ||
      !submitterUserId ||
      !Array.isArray(items) ||
      items.length === 0
    ) {
      logger.error({
        username: req.session?.user?.name,
        type: "error",
        description: `Error updating order request: Invalid request payload`,
      });
      return res
        .status(400)
        .json({ success: false, message: "Invalid request payload" });
    }

    const result = await ppeConsumables.updateOrderRequest(req.body);

    res.status(200).json({
      success: true,
      data: result,
      message: "Order request updated successfully",
    });
  } catch (err) {
    console.error("Controller Error:", err);
    logger.error({
      username: req.session?.user?.name,
      type: "error",
      description: `Error updating order request: ${err.message}`,
    });
    res.status(500).json({ success: false, error: err.message });
  }
}
async function updateOrderRequestitem(req, res) {
  try {

    const result = await ppeConsumables.updateRequestItem(req.body);

    res.status(200).json({
      success: true,
      data: result,
      message: "Order request updated successfully",
    });
  } catch (err) {
    console.error("Controller Error:", err);
    logger.error({
      username: req.session?.user?.name,
      type: "error",
      description: `Error updating order request: ${err.message}`,
    });
    res.status(500).json({ success: false, error: err.message });
  }
}
async function updateProductRequest(req, res) {
  console.log("hitting");
  try {
    // console.log("req in controller: ",req.body);
    const id = req.params.id;
    const {
      ProductName,
      TypeID,
      IsSizeRequired,
      NamePrintable,
      NewSizeNames,
      orderedSizesData,
    } = req.body;
    console.log(
      "data",
      id,
      ProductName,
      TypeID,
      IsSizeRequired,
      NamePrintable,
      NewSizeNames,
      orderedSizesData
    );

    // // Basic validation
    // if (!id || !ProductName || !TypeID || !IsSizeRequired || !NamePrintable || !NewSizeNames || !orderedSizesData) {
    //   return res.status(400).json({ success: false, message: 'Invalid request payload' });
    // }

    const result = await ppeConsumables.updateProductRequest({
      id,
      ProductName,
      TypeID,
      IsSizeRequired,
      NamePrintable,
      NewSizeNames,
      orderedSizesData,
    });

    res.status(200).json({
      success: true,
      data: result,
      message: "Product request updated successfully",
    });
  } catch (err) {
    console.error("Controller Error:", err);
    logger.error({
      username: req.session?.user?.name,
      type: "error",
      description: `Error updating product request: ${err.message}`,
    });
    res.status(500).json({ success: false, error: err.message });
  }
}
async function deleteProduct(req, res) {
  try {
    const id = req.params.id;

    const result = await ppeConsumables.deleteProduct(id);
    console.log("result", result);
    if (result.success) {
      res.status(200).json({
        success: true,
        data: result,
        message: "Product deleted successfully",
      });
    } else {
      res.status(200).json({
        success: false,
        message: result.reason,
      });
    }
  } catch (err) {
    console.error("Controller Error:", err);
    logger.error({
      username: req.session?.user?.name,
      type: "error",
      description: `Error deleting product: ${err.message}`,
    });
    res.status(500).json({ success: false, error: err.message });
  }
}

const addProduct = async (req, res) => {
  try {
    const result = await ppeConsumables.addProduct(req.body);
    res.status(200).json({
      msg: "Product added successfully",
      productId: result?.NewProductID,
    });
  } catch (error) {
    console.error("Error adding product:", error);
    logger.error({
      username: req.session?.user?.name,
      type: "error",
      description: `Error adding product: ${error.message}`,
    });
    res.status(400).send(error.message);
  }
};

const updateHiddenState = async (req,res) => {
  try{
    const result = await ppeConsumables.updateHiddenState(req.body);
    if (result.success) {
      res.status(200).json({
        success: true,
        data: result,
        message: "Product deleted successfully",
      });
    } else {
      res.status(200).json({
        success: false,
        message: result.reason,
      });
    }
    // res.status(200).json({
    //   msg: "Product updated successfully",
    //   productId: result?.NewProductID,
    // });
    return;
  } catch (error) {
    console.error("Error updating product:", error);
    logger.error({
      username: req.session?.user?.name,
      type: "error",
      description: `Error updating product: ${error.message}`,
    });
    res.status(400).send(error.message);
  }
};

module.exports = {
  allPpeRequests,
  allProducts,
  addProduct,
  sites,
  createOrderRequest,
  updateOrderRequest,
  updateOrderRequestitem,
  getOrderRequestById,
  getProductRequestById,
  updateProductRequest,
  getProductType,
  deleteProduct,
  updateHiddenState,
};
