  const updateSelectedRowStatus = (updatedStatusID, comment, params) => {
    if (updatedStatusID == 3){
      if(comment) {
        setIsValidCancelReason(true);
      } else {
        setIsValidCancelReason(false);
        return;
      }
    } 

    console.log("sssssssssssssssssssssssssssssssssssss", params);
    const apiPayload = {
      request_no: params.id,
      ProductName: params.product_name,
      Size: params.size,
      Quantity: params.quantity,
      NameForPrinting: params.NameForPrinting,
      Comments: params.comment,
      RequestItemID: params.request_item_id,
      commentOnUpdatingRequest: comment,
      action_id: updatedStatusID,
      SubmitterUserID: userData.user_id,
      SubmitterEmail: userData?.email,
      orignatorEmail: params?.orignatorEmail,
    };


    try {
      let serverAddress = apiConfig.serverAddress;
      fetch(
        `${serverAddress}ppe-consumables/update-product-request-items/${params.id}`,
        {
          method: "Post",
          headers: {
            "content-type": "application/json",
          },
          credentials: "include",
          body: JSON.stringify(apiPayload),
        }
      )
        .then((res) => {
          if (res.status === 200) {
            return res.json();
          } else if (res.status === 401) {
            toast.error("Your session has expired. Please log in again.");
            setTimeout(async () => {
              await logout();
              const redirectUrl = `/login?redirect=${encodeURIComponent(
                window.location.pathname
              )}`;
              router.push(redirectUrl);
            }, 3000);
            return null;
          } else {
            toast.error("Failed to save product.");
            setLoading(false);
          }
        })
        .then((json) => {
          if (json && json.msg) {
            toast.success(json.msg, {
              position: "top-right",
            });
            setTimeout(() => {
              router.replace("/ppe-consumable");
            }, 2000);
          }
          getData().then((data) => {
            const grouped = data?.reduce((acc, row) => {
              if (!acc[row.request_id]) {
                acc[row.request_id] = [];
              }
              acc[row.request_id].push(row);
              return acc;
            }, {});
            const STATUS_MAP = {
              1: "Pending Review",
              2: "Approved",
              3: "Rejected",
              4: "Ordered",
              5: "Draft",
            };
            const counters = {};
            const formattedData = data?.map((row) => {
              const siblings = grouped[row.request_id];
              const index = siblings.findIndex(r => r.request_item_id === row.request_item_id) + 1;

              counters[row.request_id] = (counters[row.request_id] || 0) + 1;
              return {
                id: row.id,
                request_id: `${row.request_id} - ${row.request_item_id}`, // stable per request_id
                required_date: new Date(row.required_date)
                  .toISOString()
                  .split("T")[0],
                site_name: row.site_name,
                product_name: row.product_name || "Unnamed Product",
                requestor: row.user_name,
                orignatorEmail: row.actioned_by_email,
                comment: row.comments,
                size: row.size,
                quantity: row.quantity,
                NameForPrinting: row.name_for_printing,
                status: STATUS_MAP[row.action_id],
                request_item_id: row.request_item_id,
                
              };
            });
            setRequestRowData(formattedData);
            setCancelledReasonapi("");
            setShowStatusDialog(false);
          });
        });
    } catch (error) {
      console.log("errorin submitting the order", error);
    }
  };


  export default updateSelectedRowStatus;