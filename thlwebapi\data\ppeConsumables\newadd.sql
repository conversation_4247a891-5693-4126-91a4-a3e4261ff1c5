-- ===================================================
-- Update Order Request (Parent + Items) by request_id
-- ===================================================
DECLARE @RequestID INT = 3; -- pass your request_id here
DECLARE @SubmitterEmail NVARCHAR(255);

-- Dummy data for testing
DECLARE @SubmitterUserID INT = 2;
DECLARE @TargetSiteID INT = 3;
DECLARE @RequiredDate DATE = '2024-03-15';

DECLARE @ItemsJSON NVARCHAR(MAX) = N'
[
  {
    "ProductID": 4,
    "Size": "Large",
    "Quantity": 25,
    "NameForPrinting": "John Doe",
    "Comments": "Urgent - for safety audit",
    "SizeID": 1
  },
  {
    "ProductID": 7,
    "Size": "Medium",
    "Quantity": 15,
    "NameForPrinting": "<PERSON> Smith",
    "Comments": "Regular monthly order",
    "SizeID": 2
  },
  {
    "ProductID": 8,
    "Size": "Small",
    "Quantity": 30,
    "NameForPrinting": "Mike <PERSON>",
    "Comments": "New employee onboarding",
    "SizeID": 3
  }
]';

BEGIN TRANSACTION;

BEGIN TRY
    -- 1. Lookup user details for email
    SELECT 
        @SubmitterUserID = orq.user_id,
        @SubmitterEmail = u.email
    FROM [Iss_ppe_consumables_portal].[dbo].[order_requests] orq
    JOIN [users] u
        ON orq.user_id = u.id
    WHERE orq.request_id = @RequestID;

    -- 2. Update parent order_request row
    UPDATE [Iss_ppe_consumables_portal].[dbo].[order_requests]
    SET
        site_id       = @TargetSiteID,
        required_date = @RequiredDate
		WHERE request_id = @RequestID;

    -- 3. Soft-delete existing statuses first
    UPDATE s
    SET s.is_active = 0, s.is_latest = 0
    FROM [Iss_ppe_consumables_portal].[dbo].[status] s
    INNER JOIN [Iss_ppe_consumables_portal].[dbo].[order_request_items] i
        ON s.request_item_id = i.id
    WHERE i.request_id = @RequestID;

    -- 4. Soft-delete existing items
    UPDATE [Iss_ppe_consumables_portal].[dbo].[order_request_items]
    SET is_active = 0
    WHERE request_id = @RequestID;

    -- 5. Insert updated items from JSON
    DECLARE @ItemResults TABLE (
        NewRequestItemID INT,
        ProductID INT
    );

    INSERT INTO [Iss_ppe_consumables_portal].[dbo].[order_request_items] (
        request_id,
        product_id,
        size,
        quantity,
        name_for_printing,
        comments,
        is_active,
        size_id
    )
    OUTPUT INSERTED.id, INSERTED.product_id INTO @ItemResults
    SELECT
        @RequestID,
        CAST(JSON_VALUE(value, '$.ProductID') AS INT),
        JSON_VALUE(value, '$.Size'),
        CAST(JSON_VALUE(value, '$.Quantity') AS INT),
        JSON_VALUE(value, '$.NameForPrinting'),
        JSON_VALUE(value, '$.Comments'),
        1,
        TRY_CAST(JSON_VALUE(value, '$.SizeID') AS INT)
    FROM OPENJSON(@ItemsJSON);

    -- 6. Insert new status rows for each new item
    INSERT INTO [Iss_ppe_consumables_portal].[dbo].[status] (
        request_item_id,
        action_id,
        actioned_by,
        actioned_by_email,
        actioned_at,
        comment,
        is_active,
        is_latest
    )
    SELECT
        ir.NewRequestItemID,
        3,  -- action_id = Updated
        @SubmitterUserID,
        @SubmitterEmail,
        GETDATE(),
        'Request updated by user.',
        1,
        1
    FROM @ItemResults ir;

    -- 7. Commit
    COMMIT TRANSACTION;

    -- 8. Return info
    SELECT 
        @RequestID AS UpdatedRequestID,
        (SELECT STRING_AGG(NewRequestItemID, ', ')
         FROM @ItemResults) AS UpdatedItemIDs;

END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION;

    DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
    DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
    DECLARE @ErrorState INT = ERROR_STATE();
    RAISERROR (@ErrorMessage, @ErrorSeverity, @ErrorState);
END CATCH;
