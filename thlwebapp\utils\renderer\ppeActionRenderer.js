import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faPenToSquare,
  faXmark,
  faFileExport,
  faEye,
  faInfo,
  faSquareCheck,
} from "@fortawesome/free-solid-svg-icons";
import { useState, Fragment } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { useRouter } from "next/router";
import { apiConfig } from "@/services/apiConfig";
import { toast } from "react-toastify";
import { faCircleCheck, faXmarkCircle } from "@fortawesome/free-regular-svg-icons";

const ppeActionRenderer = (
  params,
  setShowAddProduct,
  orderView,
  setIsEditingProduct,
  setEditProductData,
   setShowStatusDialog,
      setSelectedRowData,
      setStatusAction,
      userData
) => {
  // console.log("status------------------------",params.data.status);

  const router = useRouter();
  const productId = params.data.product_id;

  const [isCancelOpen, setIsCancelOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const editProduct = () => {
    setIsEditing(true);
    setIsLoading(true);
    if (typeof window !== "undefined") {
      router.push({
        pathname: `/ppe-consumable/${params.data.id}/edit`,
      });
    }
  };

    const handleRowUpdate = (actionId) => () => {
    setSelectedRowData(params.data);
    setStatusAction(actionId);
    setShowStatusDialog(true);
  };

  function deleteProduct() {
    let serverAddress = apiConfig.serverAddress;

    // setLoading(true);
    fetch(`${serverAddress}ppe-consumables/delete-product/${productId}`, {
      method: "PUT",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
      credentials: "include",
    })
      .then((res) => {
        if (res.status === 400) {
          toast.error(
            "There was an error with your request. Please check your data and try again."
          );
        } else if (res.status === 401) {
          toast.error("Your session has expired. Please log in again.");
          setTimeout(async () => {
            await logout();
            router.push("/login");
          }, 3000);
        }
        if (res.status === 200) {
          return res.json();
        }
        return Promise.reject(res);
      })
      .then((json) => {
        if (json.success) {
          toast.success("Product deleted sucessfully", {
            postition: "top-right",
          });
          router.reload()
        } else {
          toast.error("Cannot delete as product already in use");
        }
        setIsCancelOpen(false);
      })
      .catch((err) => {
        // setLoading(false);
        toast.error("Error cancelling product:", err.statusText, {
          position: "top-right",
        });
        return err;
      });
  }

  return (
    <>
      {/* Action buttons */}
      <div className="flex flex-row gap-4 justify-start text-blue-500 pl-3">
        {/* Example: view only if cancelled */}
        {params.data.status === "Cancelled" ? (
          <button title="View Request">
            <FontAwesomeIcon
              icon={faEye}
              size="lg"
              className="text-skin-primary"
            />
          </button>
        ) : (
          <>
            <button
              title={`${orderView ? (params.data.status === "Draft" || !params.data.status) ? "Edit Request" : "View Request"  : "Edit Product"}`}
              onClick={
                orderView
                  ? editProduct
                  : () => {
                      setShowAddProduct(true);
                      setIsEditingProduct(true);
                      setEditProductData(params.data);
                    }
              }
            >
              <FontAwesomeIcon
                icon={params.data.status === "Draft" || !params.data.status ? faPenToSquare : faEye}
                size="lg"
                className="text-skin-primary"
              />
            </button>
            
        <button onClick={handleRowUpdate(2)} className={`${(params.data.status !== "Pending Review" || !params.data.status) ? "hidden" : "" } ${(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5) ? "" : "hidden"} text-green-500`}>
          <FontAwesomeIcon size="lg" icon={faCircleCheck}/>
        </button>
        <button onClick={handleRowUpdate(3)} className={`${(params.data.status !== "Pending Review" || !params.data.status) ? "hidden" : "" } ${(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5) ? "" : "hidden"} text-red-500`}>
          <FontAwesomeIcon size="lg" icon={faXmarkCircle}/>
        </button>
        <button onClick={handleRowUpdate(4)} className={`${(params.data.status !== "Approved" || !params.data.status) ? "hidden" : "" } ${(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5) ? "" : "hidden"} text-green-500`}>
          <FontAwesomeIcon size="lg" icon={faSquareCheck}/>
        </button>
            {/* {!orderView && (
              //!cancel product commetned out 
              <button
                onClick={() => setIsCancelOpen(true)}
                title={`${orderView ? "Cancel Request" : "Cancel Product"}`}
                className="flex items-center"
              >
                <FontAwesomeIcon
                  icon={faXmark}
                  size="sm"
                  className="border rounded-sm border-skin-primary text-skin-primary p-1 m-0 w-[10px] h-[10px]"
                />
              </button>
            )}
             //!cancel product commetned out 
             */}

            {/* <button title="Export Request">
              <FontAwesomeIcon
                icon={faFileExport}
                size="lg"
                className="cursor-pointer text-skin-primary"
              />
            </button> */}
          </>
        )}
      </div>

      {/* Cancel modal */}
      <Transition appear show={isCancelOpen} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-10"
          onClose={() => setIsCancelOpen(false)}
        >
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex items-center justify-center min-h-full p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-[45%] transform overflow-hidden rounded-xl bg-white text-left align-middle shadow-xl transition-all">
                  <div className="relative bg-white rounded-lg shadow">
                    {/* Header */}
                    <div className="flex items-start justify-between p-8 rounded-t">
                      <h3 className="flex flex-row text-xl font-semibold text-gray-900 items-center">
                        <span className="flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4">
                          <FontAwesomeIcon icon={faInfo} />
                        </span>
                        Delete Product
                      </h3>
                      <button
                        onClick={() => setIsCancelOpen(false)}
                        type="button"
                        className="text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center"
                      >
                        <FontAwesomeIcon
                          icon={faXmark}
                          className="text-skin-primary"
                        />
                      </button>
                    </div>

                    {/* Body */}
                    <div className="p-8 py-0 space-y-6">
                      <p className="text-base leading-relaxed mt-2">
                        Are you sure you want to delete this product?
                      </p>
                    </div>

                    {/* Footer */}
                    <div className="flex items-end p-6 space-x-2 justify-end">
                      <button
                        onClick={() => setIsCancelOpen(false)}
                        type="button"
                        className="bg-skin-primary hover:bg-skin-primary font-medium rounded-md text-md text-white px-6 py-2"
                      >
                        No
                      </button>
                      <button
                        onClick={deleteProduct}
                        type="button"
                        className="bg-skin-primary hover:bg-skin-primary font-medium rounded-md text-md text-white px-6 py-2"
                      >
                        Yes
                      </button>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
};

export default ppeActionRenderer;
