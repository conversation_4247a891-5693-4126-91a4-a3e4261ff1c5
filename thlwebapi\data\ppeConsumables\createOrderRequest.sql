-- ===================================================
-- Insert New Order Request with Multiple Items
-- ===================================================
DECLARE @NewRequestID INT;
-- DECLARE @InitialStatusActionID INT = 1;

-- Capture multiple inserted items
DECLARE @ItemResults TABLE (
    NewRequestItemID INT,
    ProductID INT
);

DECLARE @nextItemNumber INT = (Select ISNULL(MAX(item_number), 0) from [Iss_ppe_consumables_portal].[dbo].[order_request_items] where request_id = @NewRequestID);

BEGIN TRANSACTION;

BEGIN TRY
    -- 1. Insert into order_requests (parent)
    INSERT INTO [Iss_ppe_consumables_portal].[dbo].[order_requests] (
        [user_id],
        [site_id],
        [created_at],
        [required_date],
        [is_active]
    )
    VALUES (
        @SubmitterUserID,
        @TargetSiteID,
        GETDATE(),
        @RequiredDate,
        1
    );

    -- 2. Capture parent ID
    SET @NewRequestID = SCOPE_IDENTITY();

    -- 3. Update request_id to match PK (if you want them identical)
    UPDATE [Iss_ppe_consumables_portal].[dbo].[order_requests]
    SET [request_id] = @NewRequestID
    WHERE [id] = @NewRequestID;

    -- 4. Insert multiple request_items from JSON
    INSERT INTO [Iss_ppe_consumables_portal].[dbo].[order_request_items] (
        [request_id],
        [product_id],
        [size],
        [quantity],
        [name_for_printing],
        [comments],
        [is_active],
        [size_id],
        [item_number]
    )
    OUTPUT INSERTED.id, INSERTED.product_id INTO @ItemResults
    SELECT
        @NewRequestID,
        CAST(JSON_VALUE(value, '$.ProductID') AS INT),
        JSON_VALUE(value, '$.Size'),
        CAST(JSON_VALUE(value, '$.Quantity') AS INT),
        JSON_VALUE(value, '$.NameForPrinting'),
        JSON_VALUE(value, '$.Comments'),
        1,  -- is_active hardcoded as 1
        CAST(JSON_VALUE(value, '$.SizeID') AS INT),
        ROW_NUMBER() OVER (ORDER BY (SELECT NULL) + @nextItemNumber)
    FROM OPENJSON(@ItemsJSON);

    -- 5. Insert initial status rows for each new item
    INSERT INTO [Iss_ppe_consumables_portal].[dbo].[status] (
        [request_item_id],
        [action_id],
        [actioned_by],
        [actioned_by_email],
        [actioned_at],
        [comment],
        [is_active],
        [is_latest]
    )
    SELECT
        ir.NewRequestItemID,
        @InitialStatusActionID,
        @SubmitterUserID,@submitterUserEmail,
        GETDATE(),
        'Request submitted by user.',
        1,
        1
    FROM @ItemResults ir

    -- 6. Commit transaction
    COMMIT TRANSACTION;

    -- 7. Return IDs
    SELECT
        @NewRequestID AS NewRequestID,
        (SELECT STRING_AGG(NewRequestItemID, ', ')
         FROM @ItemResults) AS NewRequestItemIDs;

END TRY
BEGIN CATCH
    -- Rollback everything if error
    ROLLBACK TRANSACTION;

    DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
    DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
    DECLARE @ErrorState INT = ERROR_STATE();
    RAISERROR (@ErrorMessage, @ErrorSeverity, @ErrorState);
END CATCH;
