-- 2. UPDATE A PRODUCT AND ITS SIZES
-- @ProductID, @ProductName, @ProductCode, @TypeID, @IsSizeRequired, @SizeLabel, @PackageQuantity, @Comments, @SizeIDs are parameters.

-- BEGIN TRY
--     BEGIN TRANSACTION;

--     -- A. Update the main product details
--     UPDATE [dbo].[products]
--     SET
--         [name] = @ProductName,
--         -- [product_code] = @ProductCode,
--         [type_id] = @TypeID,
--         [size_required] = @IsSizeRequired,
--         -- [size_label] = @SizeLabel,
--         -- [quantity] = @PackageQuantity,
--         -- [comments] = @Comments
--     WHERE
--         [id] = @ProductID;

--     -- B. Handle the sizes: First, deactivate all existing size links (safer than delete)
--     UPDATE [dbo].[product_available_sizes]
--     SET [is_active] = 0
--     WHERE [product_id] = @ProductID;

--     -- C. If sizes are required, insert the new list of active size links
--     IF (@IsSizeRequired = 1 AND @SizeIDs IS NOT NULL)
--     BEGIN
--         INSERT INTO [dbo].[product_available_sizes] (
--             [product_id],
--             [size_id],
--             [is_active]
--         )
--         SELECT
--             @ProductID,
--             value AS SizeID,
--             1
--         FROM
--             STRING_SPLIT(@SizeIDs, ',');
--     END

--     COMMIT TRANSACTION;

--     -- Return a success indicator
--     SELECT 1 AS Success;

-- END TRY
-- BEGIN CATCH
--     ROLLBACK TRANSACTION;
--     THROW;
-- END CATCH


BEGIN TRANSACTION;

-- Deactivate only those that are no longer present
UPDATE pas
SET is_active = 0
FROM product_available_sizes pas
WHERE pas.product_id = @ProductID
  AND pas.size_id NOT IN (SELECT value FROM STRING_SPLIT(@SizeIDs, ','))
  AND pas.is_active = 1;

-- Insert new ones that don’t already exist
INSERT INTO product_available_sizes (product_id, size_id, is_active, created_at)
SELECT @ProductID, s.value, 1, GETDATE()
FROM STRING_SPLIT(@SizeIDs, ',') s
WHERE NOT EXISTS (
    SELECT 1
    FROM product_available_sizes pas
    WHERE pas.product_id = @ProductID
      AND pas.size_id = s.value
      AND pas.is_active = 1
);

COMMIT TRANSACTION;