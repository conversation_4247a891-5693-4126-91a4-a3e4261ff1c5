import { useState, useEffect } from "react";

// Centralized permission logic that matches SideBarLinks.js
export const calculatePermissions = (userData, company, availableModules) => {
  if (!userData) return {};

  const { role_id, department_id } = userData;
  const modules = availableModules || [];

  return {
    // Module-based permissions
    canViewSuppliers: modules.includes("supplier"),
    canViewProducts: modules.includes("products") && department_id !== 5,
    canViewWhatif:
      modules.includes("whatif") &&
      ["efcltd", "flrs", "thl"].includes(company) &&
      [1].includes(department_id),
    canViewServiceLevel:
      modules.includes("serviceLevel") && [1, 2].includes(department_id),
    canViewUsers: modules.includes("users") && [1, 5, 6].includes(role_id),
    canViewLogs: modules.includes("logs") && [1, 5, 6].includes(role_id),
    canViewPpeConsumable:
      modules.includes("ppeConsumable") &&
      ["issproduce", "thl", "flrs"].includes(company),
    // Role-based permissions
    isAdmin: role_id === 1,
    isSuperAdmin: role_id === 6,
    isTHLAdmin: role_id === 5,
    isApprover: role_id === 2,
    canEditSuppliers: [1, 2].includes(role_id),
    canManageUsers: [1, 5, 6].includes(role_id),

    // Department-based permissions
    isCommercial: [1, 2].includes(department_id),
    isProduction: department_id === 5,

    // Raw data for custom checks
    role_id,
    department_id,
    company,
  };
};

// Secure permission hook
export const useSecurePermissions = (userData) => {
  const [permissions, setPermissions] = useState({});
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const calculateUserPermissions = async () => {
      if (!userData) {
        setPermissions({});
        setIsLoading(false);
        return;
      }

      try {
        // Get available modules from environment
        const availableModules =
          process.env.NEXT_PUBLIC_AVAILABLE_MODULES?.split(",") || [];

        // Get company from secure user data
        const company = userData.company;

        // Calculate permissions
        const userPermissions = calculatePermissions(
          userData,
          company,
          availableModules
        );

        setPermissions(userPermissions);
      } catch (error) {
        console.error("Error calculating permissions:", error);
        setPermissions({});
      } finally {
        setIsLoading(false);
      }
    };

    calculateUserPermissions();
  }, [userData]);

  return { permissions, isLoading };
};

// Server-side permission validation
export const validatePagePermissions = (userData, requiredPermissions) => {
  if (!userData) return false;

  const availableModules =
    process.env.NEXT_PUBLIC_AVAILABLE_MODULES?.split(",") || [];
  const permissions = calculatePermissions(
    userData,
    userData.company,
    availableModules
  );

  // Check if user has all required permissions
  return requiredPermissions.every((permission) => {
    if (typeof permission === "string") {
      return permissions[permission] === true;
    }

    if (typeof permission === "object") {
      const { type, values } = permission;

      if (type === "role_id") {
        return values.includes(userData.role_id);
      }

      if (type === "department_id") {
        return values.includes(userData.department_id);
      }

      if (type === "company") {
        return values.includes(userData.company);
      }
    }

    return false;
  });
};

// Page-specific permission definitions
export const PAGE_PERMISSIONS = {
  suppliers: ["canViewSuppliers"],
  products: ["canViewProducts"],
  whatif: ["canViewWhatif"],
  serviceLevel: ["canViewServiceLevel"],
  users: ["canViewUsers"],
  logs: ["canViewLogs"],

  // Custom permission combinations
  "supplier-edit": ["canViewSuppliers", "canEditSuppliers"],
  "admin-only": [{ type: "role_id", values: [1] }],
  "super-admin-only": [{ type: "role_id", values: [1, 6] }],
};
