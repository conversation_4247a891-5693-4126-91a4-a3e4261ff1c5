-- 4. SOFT DELETE A PRODUCT AND ITS SIZES
-- @ProductID is the parameter.

BEGIN TRY
    BEGIN TRANSACTION;

    -- A. "Soft delete" the product by deactivating it
    UPDATE [Iss_ppe_consumables_portal].[dbo].[products]
    SET [is_hidden_for_users] = @MarkHidden--1
    WHERE [id] = @ProductID;

    -- B. "Soft delete" all size links for this product
    -- UPDATE [dbo].[product_available_sizes]
    -- SET [is_hidden_for_users] = 0
    -- WHERE [product_id] = @ProductID;

    COMMIT TRANSACTION;
    SELECT 1 AS Success;

END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION;
    THROW;
END CATCH