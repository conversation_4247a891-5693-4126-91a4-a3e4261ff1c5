"use strict";
const express=require("express");
const ppeConsumablesController=require("../controllers/ppeConsumables");
const router=express.Router();


const {
    addProduct,
    allProducts,
    allPpeRequests,
    sites,
    createOrderRequest,
    updateOrderRequest,
    getOrderRequestById,
    getProductRequestById,
    updateProductRequest,
    updateOrderRequestitem,
    getProductType,
    deleteProduct,
    updateHiddenState,
} = ppeConsumablesController

const { validateSession } = require("../middleware/sessionAuth");
router.use(validateSession);

//#region get
router.get("/all-ppe-requests", allPpeRequests);
router.get("/all-products", allProducts);
router.get("/sites", sites);
router.get("/get-order-request-by-id/:id", getOrderRequestById);
router.get("/get-product-request-by-id/:id", getProductRequestById);
router.get("/get-product_types", getProductType);


//#region post
router.post("/add-product", addProduct);
router.post("/create-order-request", createOrderRequest);
router.post('/update-order-request', updateOrderRequest);
router.put('/update-product-request/:id', updateProductRequest);
router.post('/update-product-request-items/:id', updateOrderRequestitem);
router.post('/product-update-hidden-state', updateHiddenState);
router.put('/delete-product/:id',deleteProduct);

//update


module.exports=router;