
-- 1. Lookup request + user details
SELECT 
    @SubmitterUserID = orq.user_id,
    @SubmitterEmail = u.email
FROM [Iss_ppe_consumables_portal].[dbo].[order_requests] orq
JOIN [users] u
    ON orq.user_id = u.user_id
JOIN [Iss_ppe_consumables_portal].[dbo].[order_request_items] ori
    ON orq.request_id = ori.request_id
WHERE ori.id = @RequestItemID;

BEGIN TRANSACTION;
BEGIN TRY
    -- 2. Soft-delete existing statuses for this item
    UPDATE s
    SET s.is_active = 0, s.is_latest = 0
    FROM [Iss_ppe_consumables_portal].[dbo].[status] s
    WHERE s.request_item_id = @RequestItemID;


    -- 4. Insert new status row for the updated item
    INSERT INTO [Iss_ppe_consumables_portal].[dbo].[status] (
        request_item_id,
        action_id,
        actioned_by,
        actioned_by_email,
        actioned_at,
        comment,
        is_active,
        is_latest
    )
    VALUES (
        @RequestItemID,
        @action_id,               -- supply correct action_id meaning "Item Updated"
        @SubmitterUserID,
        @SubmitterEmail,
        GETDATE(),
        @commentOnUpdatingRequest, -- supply a comment from app/UI
        1,
        1
    );

    COMMIT TRANSACTION;

    -- Return info
    SELECT 
        @RequestItemID AS UpdatedItemID,
        'Success' AS Result;

END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION;
    THROW;
END CATCH;
