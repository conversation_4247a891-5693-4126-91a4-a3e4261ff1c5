// emailService.js
const { Client } = require("@microsoft/microsoft-graph-client");
const msal = require("@azure/msal-node");
const config = require("../config");

const { client_id, client_secret, tenant_id, email_sender } = config;

const clientId = client_id;
const clientSecret = client_secret;
const fs = require("fs");
const path = require("path");

const pca = new msal.ConfidentialClientApplication({
  auth: {
    clientId,
    authority: `https://login.microsoftonline.com/${tenant_id}`,
    clientSecret,
  },
});

const getEmailTemplate = (templateName) => {
  const templatePath = path.join(
    __dirname,
    `../templates/${templateName}.html`
  );

  return fs.readFileSync(templatePath, "utf8");
};

const replacePlaceholders = (template, placeholders) => {
  let emailContent = template;
  for (const [key, value] of Object.entries(placeholders)) {
    const regex = new RegExp(`\\[${key}\\]`, "g");

    emailContent = emailContent.replace(regex, value);
  }

  return emailContent;
};

const companyAddresses = {
  flrs: "dps ltd<br>57 Church Road,<br>Wimbledon,<br>SW19 5SB<br>UK",
  companyA: "dps ltd<br>57 Church Road,<br>Wimbledon,<br>SW19 5SB<br>UK",
  thl: "dps ltd<br>57 Church Road,<br>Wimbledon,<br>SW19 5SB<br>UK",
  dpsltd: "dps ltd<br>57 Church Road,<br>Wimbledon,<br>SW19 5SB<br>UK",
  "DPS MS": "dps ltd<br>57 Church Road,<br>Wimbledon,<br>SW19 5SB<br>UK",
  efcltd:
    "Unit 11a<br>Drayton Manor Drive<br>Alcester Road<br>Stratford Upon Avon<br>Warwickshire CV37 9RQ",
  "fpp-ltd":
    "Unit 4<br> Conqueror Court<br> Watermark<br> Vellum Drive<br>Sittingbourne<br>Kent<br>ME10 5BH",
  iss: "Integrated Service Solutions Ltd <br>London Road Teynham Kent ME9 9PR",
};

async function sendEmail({
  placeholders,
  emailType,
  recipientEmails,
  ccEmail,
  attachment,
  companyKey = "flrs",
  isHighImportance,
  suppliersTable,
  request_no,
  varietyCode,
  masterProductCode,
  packagingName,
}) {
  try {
    const tokenResponse = await pca.acquireTokenByClientCredential({
      scopes: ["https://graph.microsoft.com/.default"],
    });
    const accessToken = tokenResponse.accessToken;

    const graphClient = Client.init({
      authProvider: (done) => {
        done(null, accessToken);
      },
    });

    let templateName;
    let subject;
    console.log("email type: ", emailType);
    switch (emailType) {
      case "exportedProductRequest":
        templateName = "exportedProductRequest";
        subject = "TEST-New Product Request Exported";
        break;
      case "bankApproval":
        templateName = "bankApprovalEmail";
        subject = "TEST-Banking Details Approved";
        break;
      case "bankRejection":
        templateName = "bankRejectionEmail";
        subject = "TEST-Banking Details Rejected";
        break;
      case "supplierConfirmation":
        templateName = "supplierConfirmationEmail";
        subject = "TEST-Supplier Details Confirmed";
        break;
      case "exportedSupplier":
        templateName = "exportedSupplierEmail";
        subject = "TEST-Exported Supplier";
        break;
      case "incompleteSuppliers":
        templateName = "incompleteSupplierAlertEmail";
        subject = "TEST-Incomplete details Remainder";
        break;
      case "bankValidated":
        templateName = "bankValidatedEmail";
        subject = "TEST-Banking Details validated";
        break;
      case "newRawMaterialRequest":
        templateName = "productEmail";
        subject = `TEST-New Raw Material Request-${request_no}`;
        break;
      case "cancelRequest":
        templateName = "productEmail";
        subject = `TEST-Request Cancelled-${request_no}`;
        break;
      case "unblockRequest":
        templateName = "productEmail";
        subject = "TEST-Unblock request";
        break;
      case "createRequest":
        templateName = "varietyEmail";
        subject = `TEST-New Request For Review - ${request_no} - ${masterProductCode}`;
        break;
      case "ResubmittedRequest":
        templateName = "varietyEmail";
        subject = `TEST-Resubmitted Request for Review - ${request_no} - ${masterProductCode}`;
        break;
      case "approvedRequestForISS":
        templateName = "varietyEmail";
        subject = `TEST-Approved for Setup - ${request_no} - ${masterProductCode}`;
        break;
      case "RequestSubmittedToISS":
        templateName = "varietyEmail";
        subject = `TEST-Request Submitted - ${request_no} - ${masterProductCode}`;
        break;
      case "rejectedRequest":
        templateName = "varietyEmail";
        subject = `TEST-Request Rejected - ${request_no} - ${masterProductCode}`;
        break;
      case "completedRequest":
        templateName = "varietyEmail";
        subject = `TEST-Setup Completed - ${request_no} - ${masterProductCode}`;
        break;
      case "submittedPackagingRequestForISS":
        templateName = "varietyEmail";
        subject = `TEST-Packaging Request - ${request_no} - ${packagingName} - Submitted for ISS Setup`;
        break;
      case "packagingSubmittedRequest":
        templateName = "varietyEmail";
        subject = `TEST-Packaging Request - ${request_no} - ${packagingName} - Submitted to ISS`;
        break;
      case "setCompletedByISS":
        templateName = "varietyEmail";
        subject = `TEST-Packaging Request - ${request_no} - ${packagingName} - Setup Completed`;
        break;
      case "exportedPackagingRequest":
        templateName = "packagingExportEmail";
        subject = `TEST-Packaging Request Extract - ${request_no}`;
        break;
      case "submittedPPERequestForISS":
        templateName = "varietyEmail";
        subject = `TEST-PPE/ Consumable Request - ${request_no}  - Submitted for Approval`;
        break;
      case "ApprovedPPERequestForISS":
        templateName = "varietyEmail";
        subject = `TEST-PPE/ Consumable Request - ${request_no}  - Approved`;
        break;
      case "RejectedPPERequestForISS":
        templateName = "varietyEmail";
        subject = `TEST-PPE/ Consumable Request - ${request_no}  - Rejected`;
        break;
      case "OrderedPPERequestForISS":
        templateName = "varietyEmail";
        subject = `TEST-PPE/ Consumable Request - ${request_no}  - marked Ordered`;
        break;
      default:
        return "Invalid Email Type.";
    }

    let template = getEmailTemplate(templateName);
    console.log("company key", companyKey, companyAddresses[companyKey]);

    if (companyKey && companyAddresses[companyKey]) {
      console.log("template name", templateName);
      placeholders["Address"] = companyAddresses[companyKey];
    } else {
      console.log("Invalid company key.");
      return "Invalid company key.";
    }
    // Handle splitting of recipient emails
    console.log("recipients email", recipientEmails);
    const recipientEmailsArray = [];
    if (recipientEmails?.includes(";")) {
      recipientEmailsArray.push(...recipientEmails.split(";"));
    } else {
      recipientEmailsArray.push(recipientEmails);
    }
    console.log("reciepint emails array", recipientEmailsArray);
    const toRecipients = recipientEmailsArray.map((email) => ({
      emailAddress: { address: email?.trim() },
    }));

    let ccRecipients = null;

    if (ccEmail) {
      const ccEmailsArray = ccEmail.split(";");
      ccRecipients = ccEmailsArray.map((email) => ({
        emailAddress: { address: email.trim() },
      }));
    }
    console.log("ccrecipeints", ccRecipients);
    console.log("recipients", toRecipients);
    console.log("attatchments", attachment);
    const body = replacePlaceholders(template, placeholders);

    const email = {
      message: {
        subject,
        body: {
          contentType: "HTML",
          content: body,
        },
        toRecipients,
      },
    };
    if (ccRecipients) {
      email.message.ccRecipients = ccRecipients;
    }
    if (isHighImportance) {
      email.message.importance = "high";
    }
    if (attachment) {
      email.message.attachments = [
        {
          "@odata.type": "#microsoft.graph.fileAttachment",
          name: attachment.filename,
          contentBytes: attachment.content.toString("base64"),
        },
      ];
    }
    // console.log("email =",email)
    const result = await graphClient
      .api(`/users/${email_sender}/sendMail`)
      .post(email);
    console.log("email sent successfully");
    if (!result.locked) {
      return true;
    } else {
      return false;
    }
  } catch (error) {
    console.error("Error sending email:", error);
    return false;
    // throw new Error("Failed to send email");
  }
}
module.exports = {
  sendEmail,
};
