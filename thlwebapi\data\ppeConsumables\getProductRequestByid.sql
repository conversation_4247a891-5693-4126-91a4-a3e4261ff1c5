SELECT 
    p.id AS ProductID,
    p.name AS ProductName,
    p.type_id AS TypeID,
    p.name_printable AS NamePrintable,
    pas.id AS ProductAvailableSizeID,
    pas.size_id AS SizeID,
    pas.display_order AS DisplayOrder,
    pas.is_active AS SizeIsActive,
    ps.size_label AS SizeLabel
FROM [Iss_ppe_consumables_portal].[dbo].[products] p
LEFT JOIN [Iss_ppe_consumables_portal].[dbo].[product_available_sizes] pas
    ON p.id = pas.product_id
LEFT JOIN [Iss_ppe_consumables_portal].[dbo].[product_sizes] ps
    ON pas.size_id = ps.id
WHERE p.id = @ProductID and p.is_active=1
ORDER BY pas.display_order;