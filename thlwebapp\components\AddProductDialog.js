import React, { useState, useEffect } from "react";
import Select from "react-select";

import {
  Dialog,
  DialogSurface,
  DialogTitle,
  DialogBody,
  DialogActions,
  DialogContent,
  Button as FluentButton,
  Switch,
  FluentProvider,
  webLightTheme,
} from "@fluentui/react-components";
import { apiConfig } from "@/services/apiConfig";
import { toast } from "react-toastify";
import { useRouter } from "next/router";
import { logout } from "@/utils/secureStorage";
// import { logout } from "next-auth/react";

const AddProductDialog = ({
  open,
  onOpenChange,
  onSubmit,
  editProductData,
  productTypes,
  initialProductName = "",
  initialPrinting = false,
  initialSizes = [""],
  isEditingProduct = false,
}) => {
  // console.log("edit product", editProductData, productTypes);
  const router = useRouter();
  const [productName, setProductName] = useState(initialProductName);
  const [printing, setPrinting] = useState(initialPrinting);
  const [sizes, setSizes] = useState(initialSizes);
  const [newSize, setNewSize] = useState("");
  const [draggedIdx, setDraggedIdx] = useState(null);
  const [productType, setProductType] = useState(null);
  const [isProductNameValid, setIsProductNameValid] = useState(true);
  const [isProductTypeSelected, setIsProductTypeSelected] = useState(true);

  const customSelectStyles = {
    control: (base) => ({
      ...base,
      height: "28px",
      minHeight: "28px",
    }),
    valueContainer: (provided) => ({
      ...provided,
      height: "24px",
      padding: "0 6px",
    }),
    input: (provided) => ({
      ...provided,
      margin: "0px",
    }),
    indicatorSeparator: () => ({
      display: "none",
    }),
    indicatorsContainer: (provided) => ({
      ...provided,
      height: "24px",
    }),
  };

  const trimInputText = (input) => {
    return input.trim();
  };
  // Drag and drop handlers
  const handleDragStart = (idx) => setDraggedIdx(idx);

  const handleDragOver = (e, idx) => {
    e.preventDefault();
  };

  const handleDrop = (e, dropIdx) => {
    e.preventDefault();
    if (draggedIdx === null || draggedIdx === dropIdx) return;

    // Work with filtered sizes (non-empty ones)
    const filteredSizes = sizes.filter((s) => s.trim());
    const reorderedSizes = [...filteredSizes];

    // Move the dragged item
    const [draggedItem] = reorderedSizes.splice(draggedIdx, 1);
    reorderedSizes.splice(dropIdx, 0, draggedItem);

    // Update the full sizes array, preserving empty strings at the end
    const emptySizes = sizes.filter((s) => !s.trim());
    setSizes([...reorderedSizes, ...emptySizes]);
    setDraggedIdx(null);
  };

  const handleDragEnd = () => setDraggedIdx(null);

  // Add/remove size handlers
  const handleAddSize = () => {
    if (newSize.trim() !== "") {
      const trimmedSize = newSize.trim();
          const isDuplicate = sizes.some(size => 
      size.toLowerCase() === trimmedSize.toLowerCase()
    );
    
    if (isDuplicate) {
      toast.error("This size already exists");
      return;
    }

      setSizes([...sizes, newSize.trim()]);
      setNewSize("");
    }
  };

  const handleRemoveSize = (idx) => {
    setSizes(sizes.filter((_, i) => i !== idx));
  };

  const createOrderedSizesPayload = () => {
    const filteredSizes = sizes.filter((s) => s.trim()  && s.toLowerCase() !== "one size");

    // Option 1: Send sizes with explicit order indices
    const sizesWithOrder = filteredSizes.map((sizeName, index) => ({
      name: sizeName,
      displayOrder: index + 1,
    }));

    return sizesWithOrder;
  };

  useEffect(() => {
    // console.log("edit porduct type", editProductData, productTypes);
    //     {
    //     "product_id": 104,
    //     "product_name": "2ply White Laminated Z-Fold Paper Hand Towels",
    //     "category": "Consumable",
    //     "stock": 0,
    //     "sizes": "One Size",
    //     "unit": "Unit",
    //     "name_printable": false
    // }
    if (isEditingProduct && editProductData) {
      setProductName(editProductData.product_name);
      setPrinting(editProductData.name_printable);
      setProductType({
        value: editProductData.type_id,
        label: editProductData.category,
      });
      setSizes(editProductData.sizes.split(","));
    }
  }, [isEditingProduct, editProductData]);
  // console.log(
  //   "prodduct name, printing, product type, sizes",
  //   productName,
  //   printing,
  //   productType,
  //   sizes
  // );

  // Submit handler
  const handleSubmit = () => {
    if (!productName && !productType) {
      setIsProductNameValid(false);
      setIsProductTypeSelected(false);
      return;
    }else if(!productName){
      setIsProductNameValid(false);
      return;
    }else if(!productType){
      setIsProductTypeSelected(false);
      return;
    }
    let serverAddress = apiConfig.serverAddress;
    onSubmit({ productName, printing, sizes: sizes.filter((s) => s) });
    const orderedSizesData = createOrderedSizesPayload();
    console.log("ordered sizes data", orderedSizesData);

    // Determine API endpoint and method based on operation type
    const isUpdating = isEditingProduct && editProductData?.product_id;
    // console.log("edit productdata", editProductData);
    const apiEndpoint = isUpdating
      ? `${serverAddress}ppe-consumables/update-product-request/${editProductData.product_id}`
      : `${serverAddress}ppe-consumables/add-product`;

    const httpMethod = isUpdating ? "PUT" : "POST";
    console.log(
      "is updating, api endpoint, httpmethod",
      isUpdating,
      apiEndpoint,
      httpMethod
    );

    // Create payload - customize based on operation type
    const basePayload = {
      ProductName: productName.trim(),
      TypeID: productType.value,
      IsSizeRequired: 1,
      NamePrintable: printing,
      NewSizeNames: sizes.filter((s) => s.trim() && s.toLowerCase() !== "one size").join(","),
      orderedSizesData,
    };

    // Add operation-specific fields
    const apiPayload = isUpdating
      ? {
          ...basePayload,
          // For updates, only include fields that might have changed
          ProductID: editProductData.id,
          // Remove fields that shouldn't be updated or are handled differently
        }
      : {
          ...basePayload,
          // For new products, include all creation fields
          ProductCode: "from ui",
          SizeLabel: "from ui",
          PackageQuantity: "from ui",
          Comments: "from ui for medical use1",
          SizeIDs: "1,2",
        };

    console.log(`${isUpdating ? "Updating" : "Adding"} product:`, apiPayload);

    try {
      fetch(apiEndpoint, {
        method: httpMethod,
        headers: {
          "content-type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify(apiPayload),
      })
        .then((res) => {
          if (res.status === 200) {
            return res.json();
          } else if (res.status === 401) {
            toast.error("Your session has expired. Please log in again.");
            setTimeout(async () => {
              await logout();
              const redirectUrl = `/login?redirect=${encodeURIComponent(
                window.location.pathname
              )}`;
              router.push(redirectUrl);
            }, 3000);
            return null;
          } else {
            const errorMessage = isUpdating
              ? "Failed to update product."
              : "Failed to save product.";
            toast.error(errorMessage);
            if (typeof setLoading !== "undefined") {
              setLoading(false);
            }
          }
        })
        .then((json) => {
          if (json && json.msg) {
            const successMessage = isUpdating
              ? "Product updated successfully!"
              : json.msg;

            toast.success(successMessage, {
              position: "top-right",
            });

            // Reset form only after successful operation
            setProductName("");
            setPrinting(false);
            setSizes([""]);
            setNewSize("");
            setProductType(null);
          }
        })
        .catch((error) => {
          console.error(
            `Error in ${isUpdating ? "update" : "add"} product API:`,
            error
          );
          const errorMessage = isUpdating
            ? "Failed to update product. Please try again."
            : "Failed to add product. Please try again.";
          toast.error(errorMessage);
        });
    } catch (error) {
      console.error(
        `Error in ${isUpdating ? "update" : "add"} product API:`,
        error
      );
      const errorMessage = isUpdating
        ? "Failed to update product. Please try again."
        : "Failed to add product. Please try again.";
      toast.error(errorMessage);
    }
  };
  // console.log("sizes", sizes);

  return (
    <FluentProvider
      theme={webLightTheme}
      className="!bg-transparent"
      style={{ fontFamily: "poppinsregular" }}
    >
      <Dialog open={open} 
      
      onOpenChange={(event,data) => {
          if(!data.open){
            setProductName("");
            setPrinting(false);
            setSizes([""]);
            setProductType(null);
      }
      onOpenChange(event,data)
    }
      }>
      {/* onOpenChange={onOpenChange}> */}
        {/* onOpenChange{} */}
        <DialogSurface className="!bg-white">
          <DialogBody>
            <DialogTitle>
              {isEditingProduct ? "Edit Product" : "Add New Product"}
            </DialogTitle>
            <DialogContent>
              <div style={{ marginBottom: 12 }}>
                <input
                  placeholder="Product Name*"
                  value={productName}
                  max={200}
                  onChange={(e) => setProductName(e.target.value)}
                  className="flex w-full p-1 px-2 mt-2 border rounded-md"
                  onBlur={(e) => {
                    const trimmedValue = trimInputText(e.target.value);
                    setProductName(trimmedValue);
                  }}
                />
                <span className="text-red-500">
                  {!isProductNameValid && "Please enter a product name"}
                </span>
              </div>
              <div>
                {/* className=" grid grid-cols-2"> */}
                <div style={{ marginBottom: 12 }}>
                  <label className="flex align-middle items-center gap-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={printing}
                      onChange={(e) => setPrinting(e.target.checked)}
                      className="cursor-pointer"
                    />
                    <span className="labels">Name Printing</span>
                  </label>
                </div>
                <div style={{ marginBottom: 12 }}>
                  <label className="labels mb-2">Product Type</label>
                  <Select
                    value={productType}
                    onChange={setProductType}
                    options={productTypes}
                    styles={customSelectStyles}
                    className=" w-full"
                    isSearchable={false}
                    isClearable={true}
                  />
                  <span className="text-red-500">
                    {!isProductTypeSelected && "Please select a product type"}
                  </span>
                </div>
              </div>
              <div className="mb-8">
                <div className="!flex !flex-row !justify-between !items-center mb-2">
                  <label>Sizes:</label>
                  <label className="!text-xs !text-red-500">
                    Arrange sizes in the order they should appear
                  </label>
                </div>
                <div style={{ display: "flex", gap: 8, marginBottom: 8 }}>
                  <input
                    placeholder="Add size"
                    value={newSize}
                    max={90}
                    onChange={(e) => setNewSize(e.target.value)}
                    onBlur={(e) => {
                    const trimmedValue = trimInputText(e.target.value);
                    setNewSize(trimmedValue);
                  }}
                    className="flex p-1 px-2 border rounded-md"
                    style={{ flex: 1 }}
                  />
                  <button
                    className="bg-skin-primary p-1 px-3 text-white rounded-md"
                    onClick={handleAddSize}
                  >
                    Add Size Option
                  </button>
                </div>
                {!(sizes.length === 1 && sizes[0]?.toLowerCase() === "one size") && (
                <ul className="space-y-2" onDragOver={handleDragOver}>
                  {sizes
                    .filter((s) => s && s.toLowerCase() !== "one size")
                    .map((size, idx) => (
                      <li
                        key={`${size}-${idx}`}
                        className={`flex items-center justify-between bg-gray-100 rounded-md px-3 py-1 transition-shadow ${
                          draggedIdx === idx
                            ? "ring-2 ring-skin-primary opacity-50"
                            : ""
                        }`}
                        draggable
                        onDragStart={() => handleDragStart(idx)}
                        onDrop={(e) => handleDrop(e, idx)}
                        onDragEnd={handleDragEnd}
                        style={{ cursor: "grab" }}
                      >
                        <span className="inline-block bg-skin-primary/10 text-skin-primary font-medium px-3 py-1 rounded-full text-xs">
                          {size}
                        </span>
                        <FluentButton
                          size="small"
                          appearance="subtle"
                          icon={<span className="fa fa-trash text-red-500" />}
                          onClick={() => handleRemoveSize(idx)}
                          className="ml-2"
                          aria-label="Remove size"
                        >
                          Remove
                        </FluentButton>
                      </li>
                    ))}
                </ul>)
                }
              </div>
            </DialogContent>
            <DialogActions>
              <button
                className="text-black border p-1 px-5 rounded-md"
                onClick={() => {
                  onOpenChange(null, { open: false });
                  setProductName("");
                  setSizes([]);
                  setProductType(null);
                }}
              >
                Cancel
              </button>
              <button
                className="bg-skin-primary text-white p-1 px-2 rounded-md"
                onClick={handleSubmit}
              >
                {isEditingProduct ? "Update Product" : "Add Product"}
              </button>
            </DialogActions>
          </DialogBody>
        </DialogSurface>
      </Dialog>
    </FluentProvider>
  );
};

export default AddProductDialog;
