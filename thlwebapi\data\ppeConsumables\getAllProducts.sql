SELECT 
    -- Product Core Information
    p.[id] AS ProductId,
    p.[name] AS ProductName,
    -- p.[product_code] AS ProductCode,
    -- p.[comments] AS ProductComments,
    p.[is_active] AS IsProductActive,
    p.[is_hidden_for_users] AS IsProductHidden,

    -- Product Type Information (from lookup table)
    tp.[id] AS TypeId,
    tp.[name] AS ProductType,
    tp.[is_active] AS IsTypeActive,

    -- Size Information
    p.[size_required] AS IsSizeRequired,
    p.[name_printable],

    -- Ordered list of available sizes
    ISIZE.AvailableSizes,

    -- Just showing again for clarity
    p.[size_required] 

FROM 
    [Iss_ppe_consumables_portal].[dbo].[products] p
LEFT JOIN [Iss_ppe_consumables_portal].[dbo].[type_of_product] tp 
    ON p.[type_id] = tp.[id] 
    AND tp.[is_active] = 1 and p.[is_active]=1

    -- Use a subquery with STRING_AGG to get a list of all available sizes for each product
    -- This is a more advanced but very useful technique
    LEFT JOIN (
        SELECT 
            pas.[product_id],
            STRING_AGG(ps.size_label, ', ') WITHIN GROUP (ORDER BY pas.[display_order]) AS AvailableSizes
        FROM 
            [Iss_ppe_consumables_portal].[dbo].[product_available_sizes] pas
            INNER JOIN [Iss_ppe_consumables_portal].[dbo].[product_sizes] ps 
                ON pas.[size_id] = ps.[id]
        WHERE 
            pas.[is_active] = 1 
            AND ps.[is_active] = 1
        GROUP BY 
            pas.[product_id]
    ) ISIZE ON p.[id] = ISIZE.[product_id]

-- Optional: Add a WHERE clause to filter results
WHERE p.[is_active] = 1 -- Uncomment to show only active products
-- WHERE p.[type_id] = 5   -- Uncomment to show products of a specific type

ORDER BY 
    CASE 
        WHEN tp.[id] = 2 THEN 1   -- put TypeId 2 products first
        WHEN tp.[id] = 3 THEN 2   -- then TypeId 3 products
        ELSE 3                    -- all other types (if any) after
    END,
    p.[name];