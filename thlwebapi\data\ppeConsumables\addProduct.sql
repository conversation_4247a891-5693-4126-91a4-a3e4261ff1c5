BEGIN TRY
    BEGIN TRANSACTION;

    -- <PERSON>. Insert the main product details
    INSERT INTO [Iss_ppe_consumables_portal].[dbo].[products] (
        [name],
        -- [product_code],
        [type_id],
        [size_required],
        -- [size_label],
        -- [quantity],
        -- [comments],
        [is_active],
        [name_printable]
    )
    VALUES (
        @ProductName,
        -- @ProductCode,
        @TypeID,
        @IsSizeRequired,
        -- @<PERSON>zeLabel,
        -- @PackageQuantity,
        -- @Comments,
        1,
        @namePrintable
    );

    -- B. Get the ID of the newly created product
    DECLARE @NewProductID INT = SCOPE_IDENTITY();

    -- C. Handle sizes if required
    IF (@IsSizeRequired = 1)
    BEGIN
        -- Check if we have ordered sizes (JSON format) or comma-separated format
        IF (@SizesWithOrderJSON IS NOT NULL AND @SizesWithOrderJSON != '')
        BEGIN
            -- Handle ordered sizes from JSON
            -- Expected JSON format: [{"name":"Small","displayOrder":1},{"name":"Medium","displayOrder":2}]
            
            -- Parse JSON and extract sizes with their order
            ;WITH OrderedSizes AS (
                SELECT 
                    JSON_VALUE(value, '$.name') AS size_label,
                    CAST(JSON_VALUE(value, '$.displayOrder') AS INT) AS display_order
                FROM OPENJSON(@SizesWithOrderJSON)
                WHERE JSON_VALUE(value, '$.name') IS NOT NULL 
                  AND LTRIM(RTRIM(JSON_VALUE(value, '$.name'))) != ''
            )
            -- 1) Insert only missing sizes into product_sizes table
            INSERT INTO [Iss_ppe_consumables_portal].[dbo].[product_sizes] (size_label, is_active, created_at)
            SELECT DISTINCT os.size_label, 1, GETDATE()
            FROM OrderedSizes os
            LEFT JOIN [Iss_ppe_consumables_portal].[dbo].[product_sizes] ps
                ON ps.size_label = os.size_label
            WHERE ps.id IS NULL;

            -- 2) Link product to all requested sizes with display order
            ;WITH OrderedSizes AS (
                SELECT 
                    JSON_VALUE(value, '$.name') AS size_label,
                    CAST(JSON_VALUE(value, '$.displayOrder') AS INT) AS display_order
                FROM OPENJSON(@SizesWithOrderJSON)
                WHERE JSON_VALUE(value, '$.name') IS NOT NULL 
                  AND LTRIM(RTRIM(JSON_VALUE(value, '$.name'))) != ''
            )
            INSERT INTO [Iss_ppe_consumables_portal].[dbo].[product_available_sizes] (
                product_id, 
                size_id, 
                display_order, 
                is_active
            )
            SELECT 
                @NewProductID, 
                ps.id, 
                os.display_order,
                1
            FROM OrderedSizes os
            JOIN [Iss_ppe_consumables_portal].[dbo].[product_sizes] ps
                ON ps.size_label = os.size_label
            LEFT JOIN [Iss_ppe_consumables_portal].[dbo].[product_available_sizes] pas
                ON pas.product_id = @NewProductID
               AND pas.size_id = ps.id
            WHERE pas.id IS NULL; -- avoid duplicate link rows
        END
        ELSE IF (@NewSizeNames IS NOT NULL AND @NewSizeNames != '')
        BEGIN
            -- Fallback to original comma-separated logic (without order preservation)
            -- Example input: @NewSizeNames = 'S, M, L'
            ;WITH NewSizes AS (
                SELECT DISTINCT
                    NULLIF(LTRIM(RTRIM(value)), '') AS size_label,
                    ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) AS display_order
                FROM STRING_SPLIT(@NewSizeNames, ',')
                WHERE NULLIF(LTRIM(RTRIM(value)), '') IS NOT NULL
            )
            -- 1) Insert only missing sizes
            INSERT INTO [Iss_ppe_consumables_portal].[dbo].[product_sizes] (size_label, is_active, created_at)
            SELECT ns.size_label, 1, GETDATE()
            FROM NewSizes ns
            LEFT JOIN [Iss_ppe_consumables_portal].[dbo].[product_sizes] ps
                ON ps.size_label = ns.size_label
            WHERE ps.id IS NULL;

            -- 2) Link product to all requested sizes with basic ordering
            ;WITH NewSizes AS (
                SELECT DISTINCT
                    NULLIF(LTRIM(RTRIM(value)), '') AS size_label,
                    ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) AS display_order
                FROM STRING_SPLIT(@NewSizeNames, ',')
                WHERE NULLIF(LTRIM(RTRIM(value)), '') IS NOT NULL
            )
            INSERT INTO [Iss_ppe_consumables_portal].[dbo].[product_available_sizes] (
                product_id, 
                size_id, 
                display_order, 
                is_active
            )
            SELECT 
                @NewProductID, 
                ps.id, 
                ns.display_order,
                1
            FROM NewSizes ns
            JOIN [Iss_ppe_consumables_portal].[dbo].[product_sizes] ps
                ON ps.size_label = ns.size_label
            LEFT JOIN [Iss_ppe_consumables_portal].[dbo].[product_available_sizes] pas
                ON pas.product_id = @NewProductID
               AND pas.size_id = ps.id
            WHERE pas.id IS NULL; -- avoid duplicate link rows
        END
    END

    COMMIT TRANSACTION;

    -- Return success message with the new ID
    PRINT 'Product created successfully!';
    PRINT 'New Product ID: ' + CAST(@NewProductID AS VARCHAR);
    SELECT @NewProductID AS NewProductID, 'Success' AS Status;

END TRY
BEGIN CATCH
    IF @@TRANCOUNT > 0
        ROLLBACK TRANSACTION;

    PRINT 'Error occurred: ' + ERROR_MESSAGE();
    SELECT -1 AS NewProductID, 'Failed: ' + ERROR_MESSAGE() AS Status;
END CATCH