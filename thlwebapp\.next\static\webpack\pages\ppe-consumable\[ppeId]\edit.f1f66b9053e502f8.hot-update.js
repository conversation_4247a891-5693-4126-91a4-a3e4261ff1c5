"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/ppe-consumable/[ppeId]/edit",{

/***/ "./components/PpeConsumable.js":
/*!*************************************!*\
  !*** ./components/PpeConsumable.js ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_select__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-select */ \"./node_modules/react-select/dist/react-select.esm.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Dialog,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n/* harmony import */ var _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @fortawesome/free-regular-svg-icons */ \"./node_modules/@fortawesome/free-regular-svg-icons/index.mjs\");\n/* harmony import */ var _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/renderer/productStatusRenderer */ \"./utils/renderer/productStatusRenderer.js\");\n/* harmony import */ var lodash_forEach__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! lodash/forEach */ \"./node_modules/lodash/forEach.js\");\n/* harmony import */ var lodash_forEach__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(lodash_forEach__WEBPACK_IMPORTED_MODULE_12__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst customSelectStyles = {\n    control: (base)=>({\n            ...base,\n            height: \"28px\",\n            minHeight: \"28px\"\n        }),\n    valueContainer: (provided)=>({\n            ...provided,\n            height: \"28px\",\n            padding: \"0 6px\"\n        }),\n    input: (provided)=>({\n            ...provided,\n            margin: \"0px\"\n        }),\n    indicatorSeparator: ()=>({\n            display: \"none\"\n        }),\n    indicatorsContainer: (provided)=>({\n            ...provided,\n            height: \"28px\"\n        })\n};\nconst emptyItem = {\n    product: null,\n    size: null,\n    quantity: \"\",\n    nameForPrinting: \"\",\n    comments: \"\"\n};\nconst PpeConsumable = (param)=>{\n    let { userData, pageType, sites, OrderRequestData, ppeId = null } = param;\n    var _currentItem_product, _currentItem_product_sizes, _currentItem_product1, _currentItem_product2, _currentItem_product3, _currentItem_product_sizes1, _currentItem_product4, _currentItem_product5;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [nameOfOriginator, setNameOfOriginator] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [emailOfOriginator, setEmailOfOriginator] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [siteData, setSiteData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            ...emptyItem\n        }\n    ]);\n    const [requiredDate, setRequiredDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [createdDate, setCreatedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [site, setSite] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((sites === null || sites === void 0 ? void 0 : sites[0]) || null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [productsData, setProductsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [currentItem, setCurrentItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        ...emptyItem\n    });\n    const [savedItems, setSavedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // console.log(\"dsksldibiasdbilsdbv\", siteData);\n    // Add validation states for current item\n    // const [productValid, setProductValid] = useState(\"\");\n    // const [quantityValid, setQuantityValid] = useState(\"\");\n    const [dateWarning, setDateWarning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // const [isEditMode, setIsEditMode] = useState();\n    const [requestStatus, setRequestStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(5);\n    const [requestStatusList, setRequestStatusList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [submitted, setSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [popupType, setPopupType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [cancelledReasonapi, setCancelledReasonapi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isValidCancelReason, setIsValidCancelReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [editingRowId, setEditingRowId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [gridApi, setGridApi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasUnsavedChanges, setHasUnsavedChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const closeModal = (e)=>{\n        if (e) {\n            e.preventDefault();\n        }\n        setIsValidCancelReason(true);\n        setCancelledReasonapi(\"\");\n        setIsOpen(false);\n    };\n    //#region Load data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setTimeout(function() {\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].set(\"rawWarning\", true, {\n                expires: 365\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].remove(\"finishWarning\");\n        }, 2000);\n        if (pageType == \"add\" && userData) {\n            setNameOfOriginator(userData.name);\n        }\n        if (OrderRequestData || (OrderRequestData === null || OrderRequestData === void 0 ? void 0 : OrderRequestData.length) > 0) {\n            var _OrderRequestData_, _OrderRequestData_1, _OrderRequestData_2, _OrderRequestData_3;\n            // console.log(\"OrderRequestData\", OrderRequestData);\n            if ((_OrderRequestData_ = OrderRequestData[0]) === null || _OrderRequestData_ === void 0 ? void 0 : _OrderRequestData_.action_id) {\n                var _OrderRequestData_4, _OrderRequestData_5;\n                setRequestStatus((_OrderRequestData_4 = OrderRequestData[0]) === null || _OrderRequestData_4 === void 0 ? void 0 : _OrderRequestData_4.action_id);\n                if (((_OrderRequestData_5 = OrderRequestData[0]) === null || _OrderRequestData_5 === void 0 ? void 0 : _OrderRequestData_5.action_id) !== 5) {\n                    setSubmitted(true);\n                }\n            }\n            if ((_OrderRequestData_1 = OrderRequestData[0]) === null || _OrderRequestData_1 === void 0 ? void 0 : _OrderRequestData_1.site_id) {\n                var _OrderRequestData_6, _OrderRequestData_7;\n                setSite({\n                    label: (_OrderRequestData_6 = OrderRequestData[0]) === null || _OrderRequestData_6 === void 0 ? void 0 : _OrderRequestData_6.site_name,\n                    value: (_OrderRequestData_7 = OrderRequestData[0]) === null || _OrderRequestData_7 === void 0 ? void 0 : _OrderRequestData_7.site_id\n                });\n            }\n            if ((_OrderRequestData_2 = OrderRequestData[0]) === null || _OrderRequestData_2 === void 0 ? void 0 : _OrderRequestData_2.required_date) {\n                var _OrderRequestData_8, _OrderRequestData_9;\n                setRequiredDate(((_OrderRequestData_8 = OrderRequestData[0]) === null || _OrderRequestData_8 === void 0 ? void 0 : _OrderRequestData_8.required_date) ? new Date((_OrderRequestData_9 = OrderRequestData[0]) === null || _OrderRequestData_9 === void 0 ? void 0 : _OrderRequestData_9.required_date).toLocaleDateString(\"en-CA\", {\n                    year: \"numeric\",\n                    month: \"2-digit\",\n                    day: \"2-digit\"\n                }) : \"\");\n            }\n            if ((_OrderRequestData_3 = OrderRequestData[0]) === null || _OrderRequestData_3 === void 0 ? void 0 : _OrderRequestData_3.created_at) {\n                var _OrderRequestData_10, _OrderRequestData_11;\n                setCreatedDate(((_OrderRequestData_10 = OrderRequestData[0]) === null || _OrderRequestData_10 === void 0 ? void 0 : _OrderRequestData_10.created_at) ? new Date((_OrderRequestData_11 = OrderRequestData[0]) === null || _OrderRequestData_11 === void 0 ? void 0 : _OrderRequestData_11.created_at).toLocaleDateString(\"en-CA\", {\n                    year: \"numeric\",\n                    month: \"2-digit\",\n                    day: \"2-digit\"\n                }) : \"\");\n            }\n            if (OrderRequestData[0].user_name) {\n                // setNameOfOriginator();\n                setNameOfOriginator(OrderRequestData[0].user_name);\n            }\n            if (OrderRequestData[0].actioned_by_email) {\n                // setNameOfOriginator();\n                setEmailOfOriginator(OrderRequestData[0].actioned_by_email);\n            }\n            // setrowdata\n            if (OrderRequestData && Array.isArray(OrderRequestData)) {\n                console.log(\"load data\", OrderRequestData, requestStatus);\n                const STATUS_MAP = {\n                    1: \"Pending Review\",\n                    2: \"Approved\",\n                    3: \"Rejected\",\n                    4: \"Ordered\",\n                    5: \"Draft\"\n                };\n                const mappedItems = OrderRequestData.map((item)=>({\n                        id: item.order_request_items,\n                        product: {\n                            value: item.product_id,\n                            label: item.product_name\n                        },\n                        size: {\n                            value: item.size,\n                            label: item.size || \"N/A\"\n                        },\n                        quantity: item.quantity,\n                        nameForPrinting: item.name_for_printing,\n                        comments: item.comments || \"\",\n                        updateFlag: 0,\n                        status: STATUS_MAP[item.action_id]\n                    }));\n                const uniqueValues = [\n                    ...new Set(OrderRequestData.map((item)=>item.action_id))\n                ];\n                setRequestStatusList(uniqueValues);\n                setSavedItems(mappedItems);\n                console.log(\"uniqueValues\", uniqueValues);\n            }\n        }\n    }, [\n        OrderRequestData\n    ]);\n    //#endregion Load data\n    //#region ag-grid\n    const onRowSelected = (event)=>{\n        if (event.node.isSelected()) {\n            setEditingRowId(event.data.id);\n            setCurrentItem(event.data);\n        }\n    };\n    const onGridReady = (params1)=>{\n        setGridApi(params1.api);\n    };\n    // inside your component\n    const getRowClass = (params1)=>{\n        return params1.data.id === editingRowId ? \"bg-blue-100\" : \"\";\n    };\n    const IconsRenderer = (props)=>{\n        const handleDelete = (event)=>{\n            event.preventDefault();\n            const selectedRow = props.data;\n            const updatedData = savedItems.filter((item)=>item !== selectedRow);\n            setSavedItems(updatedData);\n        };\n        const handleEdit = (event)=>{\n            event.preventDefault();\n            const selectedRow = props.data;\n            setHasUnsavedChanges(true);\n            setEditingRowId(selectedRow.id);\n            // setCurrentItem(JSON.parse(JSON.stringify(selectedRow)));\n            const updatedData = savedItems.filter((item)=>item !== selectedRow);\n            setSavedItems(updatedData);\n            const handleRowUpdate1 = (actionId)=>()=>{\n                    setSelectedRowData(params.data);\n                    setStatusAction(actionId);\n                    setShowStatusDialog(true);\n                };\n            // event.preventDefault();\n            // const selectedRow = props.data;\n            // setEditingRowId(selectedRow.id || props.node.id);\n            // // const updatedData = savedItems.filter((item) => item !== selectedRow);\n            // // setSavedItems(updatedData);\n            //   setCurrentItem(JSON.parse(JSON.stringify(selectedRow)));\n            // Populate the form with the selected row data\n            setCurrentItem({\n                id: selectedRow.id,\n                product: selectedRow.product,\n                size: selectedRow.size,\n                quantity: selectedRow.quantity,\n                nameForPrinting: selectedRow.nameForPrinting,\n                nameForPrintingFlag: selectedRow.nameForPrinting,\n                comments: selectedRow.comments,\n                updateFlag: 1\n            });\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-row gap-4 justify-center text-skin-primary\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleEdit,\n                    className: \"\".concat(requestStatus !== 5 || !requestStatus ? \"hidden\" : \"\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faPenToSquare\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 266,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleDelete,\n                    className: \"\".concat(requestStatus !== 5 || !requestStatus ? \"hidden\" : \"\", \" text-red-500\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faTrash\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        onClick: handleRowUpdate(4),\n                        icon: _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faCircleCheck,\n                        className: \"\".concat(requestStatus !== 1 || !requestStatus ? \"hidden\" : \"\", \" text-green-500\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 281,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 280,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        onClick: handleRowUpdate(4),\n                        icon: _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faXmarkCircle,\n                        className: \"\".concat(requestStatus !== 1 || !requestStatus ? \"hidden\" : \"\", \" text-red-500\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 290,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 289,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        onClick: handleRowUpdate(4),\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faSquareCheck,\n                        className: \"\".concat(requestStatus !== 2 || !requestStatus ? \"hidden\" : \"\", \" text-green-500\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 299,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 298,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n            lineNumber: 265,\n            columnNumber: 7\n        }, undefined);\n    };\n    // Column definitions for the grid\n    const columnDefs = [\n        {\n            headerName: \"Product\",\n            field: \"product.label\",\n            flex: 2,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Size\",\n            field: \"size.label\",\n            flex: 1,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Quantity\",\n            field: \"quantity\",\n            flex: 1,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Name for Printing\",\n            field: \"nameForPrinting\",\n            flex: 2,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Comments\",\n            field: \"comments\",\n            flex: 2,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Actions\",\n            cellRenderer: IconsRenderer,\n            headerClass: \"header-with-border\",\n            flex: 1,\n            cellStyle: {\n                justifyContent: \"center\",\n                paddingRight: \"10px\"\n            }\n        },\n        {\n            headerName: \"Status\",\n            field: \"status\",\n            hide: requestStatus === 5 || !requestStatus,\n            cellRenderer: _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            cellStyle: ()=>({\n                    justifyContent: \"center\"\n                }),\n            flex: 1.25,\n            filterParams: {\n                values: [\n                    \"Pending Review\",\n                    \"Approved\",\n                    \"Rejected\",\n                    \"Ordered\",\n                    \"Draft\"\n                ]\n            }\n        }\n    ];\n    //#endregion ag-grid\n    //#region api's\n    function getAllProducts() {\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/all-products\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                return;\n            }\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch products\");\n        }).catch((error)=>{\n            console.error(error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to fetch products: \".concat(error.message));\n            throw error;\n        });\n    }\n    function getAllSites() {\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/sites\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                return;\n            }\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch products\");\n        });\n    }\n    //#endregion api's\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchSites = async ()=>{\n            try {\n                const siteData = await getAllSites();\n                const formattedSites = siteData === null || siteData === void 0 ? void 0 : siteData.map((site)=>({\n                        value: site.id,\n                        label: site.name\n                    }));\n                setSiteData(formattedSites);\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Error fetching Sites\", error);\n            }\n        };\n        const fetchProducts = async ()=>{\n            try {\n                const productsJson = await getAllProducts();\n                const filterOutHiddenProducts = productsJson.filter((product)=>!product.IsProductHidden);\n                const formattedProducts = filterOutHiddenProducts === null || filterOutHiddenProducts === void 0 ? void 0 : filterOutHiddenProducts.map((product)=>({\n                        value: product.ProductId,\n                        label: product.ProductName,\n                        size_required: product.IsSizeRequired || product.size_required,\n                        sizes: product.AvailableSizes ? product.AvailableSizes.split(\", \").map((size)=>({\n                                value: size.toLowerCase().replace(/\\s+/g, \"\"),\n                                label: size\n                            })) : [],\n                        productCode: product.ProductCode,\n                        packageQuantity: product.PackageQuantity,\n                        productType: product.ProductType,\n                        name_printable: product.name_printable\n                    }));\n                setProductsData(formattedProducts);\n            } catch (error) {\n                console.error(\"Error fetching products:\", error);\n            }\n        };\n        fetchProducts();\n        fetchSites();\n        setLoading(false);\n    }, []);\n    //#region Validate\n    // Update the validate function to use savedItems instead of items\n    const validate = ()=>{\n        const newErrors = {};\n        if (!site) newErrors.site = \"Site is required.\";\n        if (!requiredDate) newErrors.requiredDate = \"Required date is missing.\";\n        if (!savedItems || savedItems.length === 0) {\n            newErrors.savedItems = \"At least one item must be added.\";\n        }\n        // Update state for field-level messages\n        setErrors(newErrors);\n        const hasErrors = Object.keys(newErrors).length > 0;\n        if (hasErrors) {\n            const errorMessages = Object.values(newErrors);\n            // setPopupConfig({\n            //   title: \"Validation Error\",\n            //   message: errorMessages.join(\"\\n\"),\n            //   type: \"error\",\n            // });\n            // setShowPopup(true); // trigger popup\n            const errorsLength = Object.keys(newErrors).length;\n            if (errorsLength > 1) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please fill all required fields.\");\n            } else if (errorsLength == 1 && newErrors.savedItems) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please add at least one item.\");\n            }\n            return false;\n        }\n        return true;\n    };\n    const handleSiteAdd = (value)=>{\n        setSite(value);\n        // Clear the error for site if value is valid\n        setErrors((prev)=>{\n            const updated = {\n                ...prev\n            };\n            if (value) {\n                delete updated.site;\n            }\n            return updated;\n        });\n    };\n    // Add date validation function\n    const validateRequiredDate = (selectedDate)=>{\n        // setRequiredDate(selectedDate);\n        setErrors((prev)=>{\n            const updated = {\n                ...prev\n            };\n            if (selectedDate) {\n                delete updated.requiredDate;\n            }\n            return updated;\n        });\n        if (!selectedDate) {\n            setDateWarning(\"\");\n            return;\n        }\n        const today = new Date();\n        const selected = new Date(selectedDate);\n        const twoWeeksFromNow = new Date();\n        twoWeeksFromNow.setDate(today.getDate() + 14);\n        if (selected < twoWeeksFromNow) {\n            setDateWarning(\"Notice: Less than 2 weeks lead time may affect availability.\");\n        } else {\n            setDateWarning(\"\");\n        }\n    };\n    // #region handlers\n    const trimInputText = (input)=>{\n        return input.trim();\n    };\n    const handleCancelReason = (data)=>{\n        if (data) {\n            setIsValidCancelReason(true);\n        } else {\n            setIsValidCancelReason(false);\n        }\n    };\n    const handleSaveClick = (e)=>{\n        // e.preventDefault();\n        if (hasUnsavedChanges) {\n            setPopupType(\"unsavedWarning\");\n            setIsOpen(true); // your existing modal state\n        } else {\n            saveCurrentItem(e);\n        }\n    };\n    const saveCurrentItem = (e)=>{\n        var _currentItem_product, _currentItem_product_sizes, _currentItem_product1;\n        console.log(\"save order request\");\n        setErrors((prev)=>{\n            const updated = {\n                ...prev\n            };\n            if (e) {\n                delete updated.savedItems;\n            }\n            return updated;\n        });\n        if (!e) {\n            console.log(\"returning here\");\n            setDateWarning(\"\");\n            return;\n        }\n        e.preventDefault();\n        let count = 0;\n        if (!currentItem.product) {\n            console.log(\"++ product\");\n            count++;\n        // setProductValid(\"Please select a product\");\n        } else {\n        // setProductValid(\"\");\n        }\n        if (!currentItem.quantity || currentItem.quantity < 1) {\n            console.log(\"++ quantity\");\n            count++;\n        // setQuantityValid(\"Please enter a valid quantity\");\n        } else {\n            if (currentItem.quantity > 1 && currentItem.nameForPrinting) {\n                // currentItem.nameForPrinting=\"\"\n                setCurrentItem((prev)=>({\n                        ...prev,\n                        nameForPrinting: \"\"\n                    }));\n            }\n        // setQuantityValid(\"\");\n        }\n        if (((_currentItem_product = currentItem.product) === null || _currentItem_product === void 0 ? void 0 : _currentItem_product.size_required) && ((_currentItem_product1 = currentItem.product) === null || _currentItem_product1 === void 0 ? void 0 : (_currentItem_product_sizes = _currentItem_product1.sizes) === null || _currentItem_product_sizes === void 0 ? void 0 : _currentItem_product_sizes.length) && !currentItem.size) {\n            console.log(\"++ product size\");\n            count++;\n        // Add size validation if needed\n        }\n        if (count > 0) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please fill in all mandatory fields to add items with valid quantity\");\n            return;\n        }\n        //     if (editingRowId) {\n        //         // Update existing item\n        //   const updatedItems = savedItems.map(item =>\n        //     item.id === editingRowId ? { ...currentItem, id: editingRowId } : item\n        //   );\n        //   setSavedItems(updatedItems);\n        //   setEditingRowId(null);\n        // } else {\n        //   // Add new item\n        //   setSavedItems([...savedItems, { ...currentItem, id: Date.now() }]);\n        // }\n        if (editingRowId) {\n            // Add the edited item back to savedItems\n            setSavedItems([\n                ...savedItems,\n                {\n                    ...currentItem,\n                    id: editingRowId,\n                    product: currentItem.product ? {\n                        ...currentItem.product\n                    } : null,\n                    size: currentItem.size ? {\n                        ...currentItem.size\n                    } : null\n                }\n            ]);\n            setEditingRowId(null);\n        } else {\n            // Add new item\n            setSavedItems([\n                ...savedItems,\n                {\n                    ...currentItem,\n                    id: \"row-\".concat(Date.now())\n                }\n            ]);\n        }\n        // Reset current item\n        setCurrentItem({\n            ...emptyItem\n        });\n        setEditingRowId(null);\n        setHasUnsavedChanges(false);\n    };\n    // Handle current item changes\n    const handleCurrentItemChange = (field, value)=>{\n        if (field === \"product\") {\n            // console.log(\"field-------------\",value)\n            // Reset other fields when product changes\n            setCurrentItem((prev)=>({\n                    ...prev,\n                    [field]: value,\n                    size: null,\n                    quantity: \"\",\n                    nameForPrinting: \"\",\n                    nameForPrintingFlag: \"\",\n                    comments: \"\"\n                }));\n        } else if (field === \"quantity\" && value > 1) {\n            setCurrentItem((prev)=>({\n                    ...prev,\n                    [field]: value,\n                    nameForPrinting: \"\"\n                }));\n        } else {\n            setCurrentItem((prev)=>({\n                    ...prev,\n                    [field]: value\n                }));\n        }\n    };\n    //#region Save\n    const handleSubmit = (status_ID)=>{\n        if (status_ID != 1 && !validate()) {\n            return;\n        }\n        // console.log(\"sssssssssssssssssssssssssssssssssssssssssssssssssssss\",savedItems);\n        setLoading(true);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        // Transform savedItems to API format\n        const items = savedItems.map((item)=>{\n            var _item_product, _item_product1, _item_product2, _item_size;\n            return {\n                requestItemId: item.requestItemId,\n                // RequestItemID: typeof item.id === 'string' && item.id.startsWith('row-') ? null : item.id,\n                ProductID: (_item_product = item.product) === null || _item_product === void 0 ? void 0 : _item_product.value,\n                ProductName: ((_item_product1 = item.product) === null || _item_product1 === void 0 ? void 0 : _item_product1.label) || ((_item_product2 = item.product) === null || _item_product2 === void 0 ? void 0 : _item_product2.value),\n                Size: ((_item_size = item.size) === null || _item_size === void 0 ? void 0 : _item_size.label) || null,\n                Quantity: Number(item.quantity) || 0,\n                NameForPrinting: item.nameForPrinting || \"\",\n                Comments: item.comments || \"\"\n            };\n        });\n        const apiPayload = {\n            Status_id: status_ID,\n            SubmitterUserID: userData === null || userData === void 0 ? void 0 : userData.user_id,\n            TargetSiteID: (site === null || site === void 0 ? void 0 : site.value) || 3,\n            RequiredDate: requiredDate,\n            username: (userData === null || userData === void 0 ? void 0 : userData.email) || \"system\",\n            items: items,\n            orignatorEmail: emailOfOriginator\n        };\n        // console.log(\"items--------------------------------------------------------\",items)\n        try {\n            fetch(\"\".concat(serverAddress, \"ppe-consumables/create-order-request\"), {\n                method: \"Post\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to save product.\");\n                    setLoading(false);\n                }\n            }).then((json)=>{\n                if (json && json.msg) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(json.msg, {\n                        position: \"top-right\"\n                    });\n                    setTimeout(()=>{\n                        router.replace(\"/ppe-consumable\");\n                    }, 2000);\n                }\n            });\n        } catch (error) {\n            console.log(\"errorin submitting the order\", error);\n        }\n    // setLoading(false);\n    // setTimeout(() => {\n    //   setLoading(false);\n    //   toast.success(\"Request submitted!\");\n    //   router.push(\"/ppe-consumable\");\n    // }, 1000);\n    };\n    //#region updated conditional\n    const updateSelectedRowStatus = (updatedStatusID, existingStatusId, comment)=>{\n        const STATUS_MAP = {\n            1: \"Pending Review\",\n            2: \"Approved\",\n            3: \"Rejected\",\n            4: \"Ordered\",\n            5: \"Draft\"\n        };\n        const toBeUpdated = savedItems.filter((item)=>item.status == STATUS_MAP[existingStatusId]);\n        if (toBeUpdated.length === 0) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.warning(\"No items found with the specified status\");\n            return;\n        }\n        setLoading(true);\n        if (updatedStatusID == 3) {\n            if (cancelledReasonapi) {\n                setIsValidCancelReason(true);\n            } else {\n                console.log(\"triggerrerererered\");\n                setIsValidCancelReason(false);\n                return;\n            }\n        }\n        // console.log(\"sssssssssssssssssssssssssssssssssssss\", toBeUpdated);\n        const updatePromises = toBeUpdated.map((item)=>{\n            const apiPayload = {\n                request_no: item.id,\n                ProductName: item.product.label,\n                Size: item.size.label,\n                Quantity: item.quantity,\n                NameForPrinting: item.nameForPrinting,\n                Comments: item.comments,\n                RequestItemID: item.id,\n                commentOnUpdatingRequest: comment,\n                action_id: updatedStatusID,\n                SubmitterUserID: userData.user_id,\n                SubmitterEmail: userData === null || userData === void 0 ? void 0 : userData.email,\n                orignatorEmail: emailOfOriginator,\n                cancelledReasonapi: cancelledReasonapi\n            };\n            console.log(\"apiPayload\", apiPayload); // Log for debugging purposes.\n            return fetch(\"\".concat(_services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress, \"ppe-consumables/update-product-request-items/\").concat(item.id), {\n                method: \"POST\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then(async (res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    throw new Error(\"Unauthorized\");\n                } else {\n                    const errorText = await res.text();\n                    console.error(\"Update failed:\", errorText);\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to update item status.\");\n                    throw new Error(\"Update failed\");\n                }\n            });\n        });\n        Promise.all(updatePromises).then((results)=>{\n            const successCount = results.filter((result)=>result === null || result === void 0 ? void 0 : result.msg).length;\n            if (successCount > 0) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Successfully updated \".concat(successCount, \" item(s)\"), {\n                    position: \"top-right\"\n                });\n                setTimeout(()=>{\n                    router.replace(\"/ppe-consumable\");\n                }, 2000);\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"No items were successfully updated\");\n                router.push(\"/ppe-consumable\");\n            }\n        }).catch((error)=>{\n            console.error(\"Error updating items:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Some items failed to update\");\n        }).finally(()=>{\n            setLoading(false);\n        });\n    };\n    //#endregion updated conditional\n    // #region update\n    const handleUpdate = (action_id)=>{\n        if (!validate()) {\n            return;\n        }\n        // handleSaveClick();\n        setLoading(true);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        console.log(\"savedItems\", savedItems);\n        const items = savedItems.map((item)=>{\n            var _item_product, _item_product1, _item_product2, _item_size, _item_size1;\n            return {\n                // RequestItemID: item.id,\n                RequestItemID: typeof item.id === \"string\" && item.id.startsWith(\"row-\") ? null : item.id,\n                ProductID: item.product_id || ((_item_product = item.product) === null || _item_product === void 0 ? void 0 : _item_product.value),\n                ProductName: ((_item_product1 = item.product) === null || _item_product1 === void 0 ? void 0 : _item_product1.label) || ((_item_product2 = item.product) === null || _item_product2 === void 0 ? void 0 : _item_product2.value),\n                Size: ((_item_size = item.size) === null || _item_size === void 0 ? void 0 : _item_size.label) || null,\n                // SizeID: item.size?.value || null, //TODO no size handel\n                SizeID: ((_item_size1 = item.size) === null || _item_size1 === void 0 ? void 0 : _item_size1.value) && !isNaN(Number(item.size.value)) ? Number(item.size.value) : null,\n                Quantity: Number(item.quantity) || 0,\n                NameForPrinting: item.nameForPrinting || \"\",\n                Comments: item.comments || \"\"\n            };\n        });\n        const apiPayload = {\n            requestId: ppeId,\n            action_id: action_id,\n            submitterUserId: userData.user_id,\n            username: (userData === null || userData === void 0 ? void 0 : userData.email) || \"system\",\n            targetSiteId: site.value,\n            requiredDate: requiredDate,\n            items: items,\n            comment: cancelledReasonapi,\n            orignatorEmail: emailOfOriginator\n        };\n        console.log(\"apiPayload--------------------------------------------------------\", apiPayload);\n        try {\n            fetch(\"\".concat(serverAddress, \"ppe-consumables/update-order-request\"), {\n                method: \"Post\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to save product.\");\n                    setLoading(false);\n                }\n            }).then((json)=>{\n                if (json && json.msg) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(json.msg, {\n                        position: \"top-right\"\n                    });\n                    setTimeout(()=>{\n                        router.replace(\"/ppe-consumable\");\n                    }, 2000);\n                }\n            });\n        } catch (error) {\n            console.log(\"errorin submitting the order\", error);\n        }\n        setLoading(false);\n        setTimeout(()=>{\n            setLoading(false);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Request submitted!\");\n            router.push(\"/ppe-consumable\");\n        }, 1000);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setTimeout(function() {\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].set(\"rawWarning\", true, {\n                expires: 365\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].remove(\"finishWarning\");\n        }, 2000);\n        if (pageType == \"update\") {\n            setIsEdit(true);\n        }\n        if (pageType == \"add\" && userData) {\n            setNameOfOriginator(userData.name);\n        }\n    }, [\n        0\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"flex flex-col justify-start max-w-full  mx-auto mr-14\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row gap-8 bg-white p-6 rounded-lg shadow-[0_0_1px_rgba(0,0,0,0.1)]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: \"Full Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1015,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: nameOfOriginator || \"\",\n                                            disabled: true,\n                                            className: \"block w-full px-4 text-gray-500 border rounded-lg form-input\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1016,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1014,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1024,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: emailOfOriginator || (userData === null || userData === void 0 ? void 0 : userData.email) || \"\",\n                                            disabled: true,\n                                            className: \"block w-full px-4 text-gray-500 border rounded-lg form-input\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1025,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1023,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: \"Created Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1033,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: new Date().toLocaleDateString(\"en-CA\"),\n                                            disabled: true,\n                                            className: \"block w-full px-4 text-gray-500 border rounded-lg form-input\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1034,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1032,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: [\n                                                \"Site\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500 ml-[2px]\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1043,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1042,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            value: site,\n                                            onChange: (e)=>{\n                                                setSite();\n                                                handleSiteAdd(e);\n                                            },\n                                            options: siteData,\n                                            isDisabled: (sites === null || sites === void 0 ? void 0 : sites.length) === 1 || submitted,\n                                            styles: customSelectStyles,\n                                            isClearable: true,\n                                            className: \" w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1045,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        errors.site && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: \"#ef4444\"\n                                            },\n                                            children: errors.site\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1057,\n                                            columnNumber: 31\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1041,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: [\n                                                \"Required Date \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1061,\n                                                    columnNumber: 31\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1060,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            min: new Date().toISOString().split(\"T\")[0],\n                                            value: requiredDate,\n                                            onChange: (e)=>{\n                                                setRequiredDate(e.target.value);\n                                                validateRequiredDate(e.target.value);\n                                            },\n                                            className: \"block w-full px-4 border rounded-lg form-input\",\n                                            disabled: submitted\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1063,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        dateWarning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-orange-500 text-sm\",\n                                            children: dateWarning\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1075,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        errors.requiredDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: \"#ef4444\"\n                                            },\n                                            children: errors.requiredDate\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1078,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1059,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                            lineNumber: 1013,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 1007,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-row justify-between items-end my-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"flex mb-1 font-semibold tracking-wider text-base pl-2\",\n                                    children: \"Items\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1098,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1097,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col mb-28px bg-white p-6 rounded-lg py-8 shadow-[0_0_2px_rgba(0,0,0,0.2)]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-row gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: [\n                                                            \"Product \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                lineNumber: 1107,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1106,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        value: currentItem.product,\n                                                        onChange: (val)=>handleCurrentItemChange(\"product\", val),\n                                                        options: productsData,\n                                                        styles: customSelectStyles,\n                                                        className: \"reactSelectCustom w-full\",\n                                                        isSearchable: true,\n                                                        isClearable: true,\n                                                        isDisabled: submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1109,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1105,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: [\n                                                            \"Size\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 \".concat(!((_currentItem_product = currentItem.product) === null || _currentItem_product === void 0 ? void 0 : _currentItem_product.size_required) || !((_currentItem_product1 = currentItem.product) === null || _currentItem_product1 === void 0 ? void 0 : (_currentItem_product_sizes = _currentItem_product1.sizes) === null || _currentItem_product_sizes === void 0 ? void 0 : _currentItem_product_sizes.length) || submitted ? \"hidden\" : \"\"),\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                lineNumber: 1123,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1121,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        value: currentItem.size,\n                                                        onChange: (val)=>handleCurrentItemChange(\"size\", val),\n                                                        options: ((_currentItem_product2 = currentItem.product) === null || _currentItem_product2 === void 0 ? void 0 : _currentItem_product2.sizes) || [],\n                                                        styles: customSelectStyles,\n                                                        className: \"reactSelectCustom w-full\",\n                                                        isSearchable: true,\n                                                        isClearable: true,\n                                                        isDisabled: !((_currentItem_product3 = currentItem.product) === null || _currentItem_product3 === void 0 ? void 0 : _currentItem_product3.size_required) || !((_currentItem_product4 = currentItem.product) === null || _currentItem_product4 === void 0 ? void 0 : (_currentItem_product_sizes1 = _currentItem_product4.sizes) === null || _currentItem_product_sizes1 === void 0 ? void 0 : _currentItem_product_sizes1.length) || submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1135,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1120,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: [\n                                                            \"Quantity \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                lineNumber: 1152,\n                                                                columnNumber: 28\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1151,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        // min={0}\n                                                        max: 999,\n                                                        value: currentItem.quantity,\n                                                        onChange: (e)=>handleCurrentItemChange(\"quantity\", e.target.value > 999 ? 999 : parseInt(e.target.value)),\n                                                        className: \"block w-full px-4 border rounded-lg form-input\",\n                                                        disabled: submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1154,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1150,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: \"Name for Printing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1170,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        maxLength: \"50\" //TODO change in db also\n                                                        ,\n                                                        value: currentItem.nameForPrinting,\n                                                        onChange: (e)=>handleCurrentItemChange(\"nameForPrinting\", e.target.value),\n                                                        disabled: currentItem.quantity !== 1 || submitted || !((_currentItem_product5 = currentItem.product) === null || _currentItem_product5 === void 0 ? void 0 : _currentItem_product5.name_printable),\n                                                        className: \"block w-full px-4 border rounded-lg form-input\",\n                                                        placeholder: \"Only if quantity = 1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1171,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1169,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: \"Comments\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1188,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        value: currentItem.comments,\n                                                        maxLength: \"500\",\n                                                        onChange: (e)=>handleCurrentItemChange(\"comments\", e.target.value),\n                                                        className: \"disabled:text-gray-400 disabled:bg-[#F6F3F3] block w-full h-8 px-4 border rounded-lg form-input resize-none\",\n                                                        rows: 1,\n                                                        disabled: submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1189,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1187,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-end\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    onClick: saveCurrentItem,\n                                                    // onClick={handleSaveClick}\n                                                    className: \"px-2 py-1 2xl:px-3.5 border border-skin-primary rounded-md text-skin-primary cursor-pointer \".concat(submitted ? \"hidden\" : \"\"),\n                                                    disabled: submitted,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faFloppyDisk\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1210,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1202,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1200,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1104,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ag-theme-alpine my-[24px]\",\n                                        style: {\n                                            height: 250,\n                                            width: \"100%\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_7__.AgGridReact, {\n                                            ref: gridRef,\n                                            columnDefs: columnDefs,\n                                            rowData: savedItems,\n                                            rowHeight: 35,\n                                            getRowClass: getRowClass,\n                                            onGridReady: onGridReady\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1221,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1217,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    popupType === \"unsavedWarning\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"One item is being edited. If you continue, changes will be lost.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1239,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-end gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: closeModal,\n                                                        className: \"border px-4 py-2 rounded\",\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1244,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            saveCurrentItem(); // discard editing row\n                                                            closeModal();\n                                                        },\n                                                        className: \"bg-blue-600 text-white px-4 py-2 rounded\",\n                                                        children: \"Save Anyway\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1250,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1243,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1238,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1103,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 1096,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row justify-end gap-4 rounded-lg  p-4 mt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    router.push(\"/ppe-consumable\");\n                                },\n                                className: \"border border-skin-primary text-skin-primary rounded-md px-6\",\n                                disabled: loading,\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1266,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (pageType === \"edit\") {\n                                        console.log(\"hasUnsavedChanges\", hasUnsavedChanges);\n                                        if (hasUnsavedChanges) {\n                                            setPopupType(\"Save\");\n                                            setIsOpen(true);\n                                        } else {\n                                            handleUpdate(5);\n                                        }\n                                    } else {\n                                        handleSubmit(5);\n                                    }\n                                },\n                                disabled: loading || requestStatus === 1,\n                                className: \"border border-skin-primary text-skin-primary rounded-md px-6 \".concat(requestStatus !== 5 ? \"hidden\" : \"\"),\n                                children: \"Save\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1276,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (hasUnsavedChanges) {\n                                        setPopupType(\"submitWhileEditing\");\n                                        setIsOpen(true);\n                                    } else {\n                                        if (!validate()) return;\n                                        setPopupType(\"submit\");\n                                        setIsOpen(true);\n                                    }\n                                },\n                                className: \"border border-skin-primary bg-skin-primary text-white rounded-md px-6 font-medium \".concat(requestStatus !== 5 ? \"hidden\" : \"\"),\n                                disabled: loading || requestStatus === 1,\n                                children: \"Submit\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1298,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (!validate()) return;\n                                    setPopupType(\"reject\");\n                                    setIsOpen(true);\n                                },\n                                className: \"bg-red-600 text-white rounded-md px-6 font-medium \".concat(!requestStatusList.includes(1) ? \"hidden\" : \"\", \"  \").concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\"),\n                                children: \"Reject All Pending Requests\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1317,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (!validate()) return;\n                                    setPopupType(\"approve\");\n                                    setIsOpen(true);\n                                },\n                                className: \"bg-green-600 text-white rounded-md px-6 font-medium \".concat(!requestStatusList.includes(1) ? \"hidden\" : \"\", \"  \").concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\"),\n                                children: \"Approve All Pending Requests\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1336,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    //   if (pageType === \"add\") {\n                                    //     handleSubmit(1);\n                                    //   } else {\n                                    updateSelectedRowStatus(4, 2);\n                                //   }\n                                },\n                                className: \"bg-green-600 text-white rounded-md px-6 font-medium \".concat(requestStatusList.includes(2) ? \"\" : \"hidden\", \"  \").concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\"),\n                                children: \"Mark All Approved Requests to Ordered\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1355,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 1265,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                lineNumber: 1003,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Transition, {\n                appear: true,\n                show: isOpen,\n                as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Dialog, {\n                    as: \"div\",\n                    className: \"relative z-10\",\n                    onClose: (value)=>{\n                        if (popupType !== \"reject\" || isValidCancelReason) {\n                            closeModal();\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Transition.Child, {\n                            as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                            enter: \"ease-out duration-300\",\n                            enterFrom: \"opacity-0\",\n                            enterTo: \"opacity-100\",\n                            leave: \"ease-in duration-200\",\n                            leaveFrom: \"opacity-100\",\n                            leaveTo: \"opacity-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1399,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                            lineNumber: 1390,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center min-h-full p-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Transition.Child, {\n                                    as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                                    enter: \"ease-out duration-300\",\n                                    enterFrom: \"opacity-0 scale-95\",\n                                    enterTo: \"opacity-100 scale-100\",\n                                    leave: \"ease-in duration-200\",\n                                    leaveFrom: \"opacity-100 scale-100\",\n                                    leaveTo: \"opacity-0 scale-95\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Dialog.Panel, {\n                                        className: \" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-white rounded-lg shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between p-8 rounded-t\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"flex flex-row text-xl font-semibold text-gray-900  items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faInfo\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                            lineNumber: 1420,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1419,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \",\n                                                                \"Confirm \".concat(popupType == \"submit\" ? \"Submission\" : popupType == \"reject\" ? \"Rejection\" : popupType == \"approve\" ? \"Approval\" : popupType == \"markOrdered\" ? \"markOrdered\" : popupType == \"Save\" ? \"Save\" : popupType == \"submitWhileEditing\" ? \"Submission\" : \" \")\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1418,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: closeModal,\n                                                            type: \"button\",\n                                                            className: \"text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center \",\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                                                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faXmark,\n                                                                    className: \"text-skin-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1444,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1438,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1417,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-8 py-0 space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-base xl:text-md 2xl:text-lg leading-relaxed mt-2\",\n                                                            children: [\n                                                                \"Are you sure you want to\",\n                                                                \" \",\n                                                                popupType == \"submit\" ? \"submit\" : popupType == \"reject\" ? \"reject\" : popupType == \"approve\" ? \"approve\" : popupType == \"markOrdered\" ? \"mark approved items as ordered\" : popupType == \"Save\" ? \"save? there is a item being edited,\\n it will be lost if you Save \" : popupType == \"submitWhileEditing\" ? \"Submit? there is a item being edited,\\n it will be lost if you Submit \" : \" \",\n                                                                \" \",\n                                                                \"this request?\",\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1452,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        popupType == \"reject\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                    className: \"flex flex-col w-full rounded-md p-2 px-3  border border-light-gray2\",\n                                                                    rows: \"8\",\n                                                                    value: cancelledReasonapi,\n                                                                    onChange: (e)=>{\n                                                                        setCancelledReasonapi(e.target.value);\n                                                                        if (e.target.value) {\n                                                                            setIsValidCancelReason(true);\n                                                                        }\n                                                                    },\n                                                                    onBlur: (e)=>{\n                                                                        const trimmedValue = trimInputText(e.target.value);\n                                                                        setCancelledReasonapi(trimmedValue);\n                                                                    },\n                                                                    placeholder: \"Provide reason for rejection...\",\n                                                                    maxLength: \"500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1472,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                popupType == \"reject\" && !isValidCancelReason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-500\",\n                                                                    children: \"Please Provide reason for cancellation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1492,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1470,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1451,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-end p-6 space-x-2 justify-end\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                closeModal(), setCancelledReasonapi(\"\");\n                                                            },\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            type: \"button\",\n                                                            className: \"border border-skin-primary text-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                            children: \"Cancel\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1501,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                if (popupType === \"reject\" && !(cancelledReasonapi === null || cancelledReasonapi === void 0 ? void 0 : cancelledReasonapi.trim())) {\n                                                                    setIsValidCancelReason(false);\n                                                                    return;\n                                                                }\n                                                                if (pageType == \"add\") {\n                                                                    if (popupType == \"submit\") {\n                                                                        handleSubmit(1);\n                                                                    } else if (popupType === \"submitWhileEditing\") {\n                                                                        handleSubmit(1);\n                                                                    }\n                                                                } else {\n                                                                    if (popupType == \"submit\") {\n                                                                        handleUpdate(1);\n                                                                    } else if (popupType === \"reject\") {\n                                                                        updateSelectedRowStatus(3, 1);\n                                                                    } else if (popupType === \"approve\") {\n                                                                        updateSelectedRowStatus(2, 1);\n                                                                    } else if (popupType === \"Save\") {\n                                                                        handleUpdate(5);\n                                                                    } else if (popupType === \"markOrdered\") {\n                                                                        updateSelectedRowStatus(4, 2);\n                                                                    } else if (popupType === \"submitWhileEditing\") {\n                                                                        handleUpdate(1);\n                                                                    }\n                                                                }\n                                                                setCancelledReasonapi(\"\");\n                                                                closeModal();\n                                                            },\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            type: \"button\",\n                                                            className: \"text-white bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center h-full border border-skin-primary\",\n                                                            children: \"Continue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1511,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1500,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1415,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1413,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1404,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1403,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                            lineNumber: 1402,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 1381,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                lineNumber: 1380,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(PpeConsumable, \"O68D4V6/DUUfJI1W3US3XF3lslU=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = PpeConsumable;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PpeConsumable);\nvar _c;\n$RefreshReg$(_c, \"PpeConsumable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1BwZUNvbnN1bWFibGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXFFO0FBQ25DO0FBQ0s7QUFDQztBQUNSO0FBQ2lCO0FBQ2dCO0FBT3RCO0FBQ0M7QUFDRztBQUNRO0FBQ21CO0FBQ25CO0FBQ1I7QUFJRjtBQUM4QjtBQUMxQztBQUVqQyxNQUFNeUIscUJBQXFCO0lBQ3pCQyxTQUFTLENBQUNDLE9BQVU7WUFDbEIsR0FBR0EsSUFBSTtZQUNQQyxRQUFRO1lBQ1JDLFdBQVc7UUFDYjtJQUNBQyxnQkFBZ0IsQ0FBQ0MsV0FBYztZQUM3QixHQUFHQSxRQUFRO1lBQ1hILFFBQVE7WUFDUkksU0FBUztRQUNYO0lBQ0FDLE9BQU8sQ0FBQ0YsV0FBYztZQUNwQixHQUFHQSxRQUFRO1lBQ1hHLFFBQVE7UUFDVjtJQUNBQyxvQkFBb0IsSUFBTztZQUN6QkMsU0FBUztRQUNYO0lBQ0FDLHFCQUFxQixDQUFDTixXQUFjO1lBQ2xDLEdBQUdBLFFBQVE7WUFDWEgsUUFBUTtRQUNWO0FBQ0Y7QUFFQSxNQUFNVSxZQUFZO0lBQ2hCQyxTQUFTO0lBQ1RDLE1BQU07SUFDTkMsVUFBVTtJQUNWQyxpQkFBaUI7SUFDakJDLFVBQVU7QUFDWjtBQUNBLE1BQU1DLGdCQUFnQjtRQUFDLEVBQ3JCQyxRQUFRLEVBQ1JDLFFBQVEsRUFDUkMsS0FBSyxFQUNMQyxnQkFBZ0IsRUFDaEJDLFFBQVEsSUFBSSxFQUNiO1FBb2lDc0JDLHNCQUNBQSw0QkFBQUEsdUJBWUlBLHVCQU1OQSx1QkFDQUEsNkJBQUFBLHVCQW9DQUE7O0lBM2xDbkIsTUFBTSxDQUFDQyxRQUFRQyxVQUFVLEdBQUdqRCwrQ0FBUUEsQ0FBQztJQUNyQyxNQUFNLENBQUNrRCxrQkFBa0JDLG9CQUFvQixHQUFHbkQsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDb0QsbUJBQW1CQyxxQkFBcUIsR0FBR3JELCtDQUFRQSxDQUFDO0lBQzNELE1BQU0sQ0FBQ3NELFVBQVVDLFlBQVksR0FBR3ZELCtDQUFRQSxDQUFDLENBQUM7SUFFMUMsTUFBTSxDQUFDd0QsT0FBT0MsU0FBUyxHQUFHekQsK0NBQVFBLENBQUM7UUFBQztZQUFFLEdBQUdtQyxTQUFTO1FBQUM7S0FBRTtJQUNyRCxNQUFNLENBQUN1QixjQUFjQyxnQkFBZ0IsR0FBRzNELCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQzRELGFBQWFDLGVBQWUsR0FBRzdELCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQzhELE1BQU1DLFFBQVEsR0FBRy9ELCtDQUFRQSxDQUFDNEMsQ0FBQUEsa0JBQUFBLDRCQUFBQSxLQUFPLENBQUMsRUFBRSxLQUFJO0lBQy9DLE1BQU0sQ0FBQ29CLFNBQVNDLFdBQVcsR0FBR2pFLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ2tFLGNBQWNDLGdCQUFnQixHQUFHbkUsK0NBQVFBO0lBQ2hELE1BQU1vRSxTQUFTaEUsc0RBQVNBO0lBQ3hCLE1BQU0sQ0FBQzJDLGFBQWFzQixlQUFlLEdBQUdyRSwrQ0FBUUEsQ0FBQztRQUFFLEdBQUdtQyxTQUFTO0lBQUM7SUFDOUQsTUFBTSxDQUFDbUMsWUFBWUMsY0FBYyxHQUFHdkUsK0NBQVFBLENBQUMsRUFBRTtJQUMvQyxNQUFNd0UsVUFBVXpFLDZDQUFNQSxDQUFDO0lBQ3ZCLGdEQUFnRDtJQUVoRCx5Q0FBeUM7SUFDekMsd0RBQXdEO0lBQ3hELDBEQUEwRDtJQUUxRCxNQUFNLENBQUMwRSxhQUFhQyxlQUFlLEdBQUcxRSwrQ0FBUUEsQ0FBQztJQUUvQyxrREFBa0Q7SUFDbEQsTUFBTSxDQUFDMkUsZUFBZUMsaUJBQWlCLEdBQUc1RSwrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNLENBQUM2RSxtQkFBbUJDLHFCQUFxQixHQUFHOUUsK0NBQVFBLENBQUM7SUFDM0QsTUFBTSxDQUFDK0UsUUFBUUMsVUFBVSxHQUFHaEYsK0NBQVFBLENBQUMsQ0FBQztJQUN0QyxNQUFNLENBQUNpRixXQUFXQyxhQUFhLEdBQUdsRiwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUNtRixXQUFXQyxhQUFhLEdBQUdwRiwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUNxRixvQkFBb0JDLHNCQUFzQixHQUFHdEYsK0NBQVFBLENBQUM7SUFDN0QsTUFBTSxDQUFDdUYscUJBQXFCQyx1QkFBdUIsR0FBR3hGLCtDQUFRQSxDQUFDO0lBQy9ELE1BQU0sQ0FBQ3lGLGNBQWNDLGdCQUFnQixHQUFHMUYsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDMkYsU0FBU0MsV0FBVyxHQUFHNUYsK0NBQVFBLENBQUM7SUFFdkMsTUFBTSxDQUFDNkYsbUJBQW1CQyxxQkFBcUIsR0FBRzlGLCtDQUFRQSxDQUFDO0lBRTNELE1BQU0rRixhQUFhLENBQUNDO1FBQ2xCLElBQUlBLEdBQUc7WUFDTEEsRUFBRUMsY0FBYztRQUNsQjtRQUNBVCx1QkFBdUI7UUFDdkJGLHNCQUFzQjtRQUN0QnJDLFVBQVU7SUFDWjtJQUNBLG1CQUFtQjtJQUNuQm5ELGdEQUFTQSxDQUFDO1FBQ1JvRyxXQUFXO1lBQ1Q3RixxREFBVyxDQUFDLGNBQWMsTUFBTTtnQkFBRStGLFNBQVM7WUFBSTtZQUMvQy9GLHdEQUFjLENBQUM7UUFDakIsR0FBRztRQUVILElBQUlzQyxZQUFZLFNBQVNELFVBQVU7WUFDakNTLG9CQUFvQlQsU0FBUzRELElBQUk7UUFDbkM7UUFDQSxJQUFJekQsb0JBQW9CQSxDQUFBQSw2QkFBQUEsdUNBQUFBLGlCQUFrQjBELE1BQU0sSUFBRyxHQUFHO2dCQUVoRDFELG9CQU1BQSxxQkFNQUEscUJBZUFBO1lBNUJKLHFEQUFxRDtZQUNyRCxLQUFJQSxxQkFBQUEsZ0JBQWdCLENBQUMsRUFBRSxjQUFuQkEseUNBQUFBLG1CQUFxQjJELFNBQVMsRUFBRTtvQkFDakIzRCxxQkFDYkE7Z0JBREorQixrQkFBaUIvQixzQkFBQUEsZ0JBQWdCLENBQUMsRUFBRSxjQUFuQkEsMENBQUFBLG9CQUFxQjJELFNBQVM7Z0JBQy9DLElBQUkzRCxFQUFBQSxzQkFBQUEsZ0JBQWdCLENBQUMsRUFBRSxjQUFuQkEsMENBQUFBLG9CQUFxQjJELFNBQVMsTUFBSyxHQUFHO29CQUN4Q3RCLGFBQWE7Z0JBQ2Y7WUFDRjtZQUNBLEtBQUlyQyxzQkFBQUEsZ0JBQWdCLENBQUMsRUFBRSxjQUFuQkEsMENBQUFBLG9CQUFxQjRELE9BQU8sRUFBRTtvQkFFdkI1RCxxQkFDQUE7Z0JBRlRrQixRQUFRO29CQUNOMkMsS0FBSyxHQUFFN0Qsc0JBQUFBLGdCQUFnQixDQUFDLEVBQUUsY0FBbkJBLDBDQUFBQSxvQkFBcUI4RCxTQUFTO29CQUNyQ0MsS0FBSyxHQUFFL0Qsc0JBQUFBLGdCQUFnQixDQUFDLEVBQUUsY0FBbkJBLDBDQUFBQSxvQkFBcUI0RCxPQUFPO2dCQUNyQztZQUNGO1lBQ0EsS0FBSTVELHNCQUFBQSxnQkFBZ0IsQ0FBQyxFQUFFLGNBQW5CQSwwQ0FBQUEsb0JBQXFCZ0UsYUFBYSxFQUFFO29CQUVwQ2hFLHFCQUNhQTtnQkFGZmMsZ0JBQ0VkLEVBQUFBLHNCQUFBQSxnQkFBZ0IsQ0FBQyxFQUFFLGNBQW5CQSwwQ0FBQUEsb0JBQXFCZ0UsYUFBYSxJQUM5QixJQUFJQyxNQUFLakUsc0JBQUFBLGdCQUFnQixDQUFDLEVBQUUsY0FBbkJBLDBDQUFBQSxvQkFBcUJnRSxhQUFhLEVBQUVFLGtCQUFrQixDQUM3RCxTQUNBO29CQUNFQyxNQUFNO29CQUNOQyxPQUFPO29CQUNQQyxLQUFLO2dCQUNQLEtBRUY7WUFFUjtZQUVBLEtBQUlyRSxzQkFBQUEsZ0JBQWdCLENBQUMsRUFBRSxjQUFuQkEsMENBQUFBLG9CQUFxQnNFLFVBQVUsRUFBRTtvQkFFakN0RSxzQkFDYUE7Z0JBRmZnQixlQUNFaEIsRUFBQUEsdUJBQUFBLGdCQUFnQixDQUFDLEVBQUUsY0FBbkJBLDJDQUFBQSxxQkFBcUJzRSxVQUFVLElBQzNCLElBQUlMLE1BQUtqRSx1QkFBQUEsZ0JBQWdCLENBQUMsRUFBRSxjQUFuQkEsMkNBQUFBLHFCQUFxQnNFLFVBQVUsRUFBRUosa0JBQWtCLENBQzFELFNBQ0E7b0JBQ0VDLE1BQU07b0JBQ05DLE9BQU87b0JBQ1BDLEtBQUs7Z0JBQ1AsS0FFRjtZQUVSO1lBQ0EsSUFBSXJFLGdCQUFnQixDQUFDLEVBQUUsQ0FBQ3VFLFNBQVMsRUFBRTtnQkFDakMseUJBQXlCO2dCQUN6QmpFLG9CQUFvQk4sZ0JBQWdCLENBQUMsRUFBRSxDQUFDdUUsU0FBUztZQUNuRDtZQUNBLElBQUl2RSxnQkFBZ0IsQ0FBQyxFQUFFLENBQUN3RSxpQkFBaUIsRUFBRTtnQkFDekMseUJBQXlCO2dCQUN6QmhFLHFCQUFxQlIsZ0JBQWdCLENBQUMsRUFBRSxDQUFDd0UsaUJBQWlCO1lBQzVEO1lBQ0EsYUFBYTtZQUNiLElBQUl4RSxvQkFBb0J5RSxNQUFNQyxPQUFPLENBQUMxRSxtQkFBbUI7Z0JBQ3ZEMkUsUUFBUUMsR0FBRyxDQUFDLGFBQWE1RSxrQkFBa0I4QjtnQkFDM0MsTUFBTStDLGFBQWE7b0JBQ2pCLEdBQUc7b0JBQ0gsR0FBRztvQkFDSCxHQUFHO29CQUNILEdBQUc7b0JBQ0gsR0FBRztnQkFDTDtnQkFDQSxNQUFNQyxjQUFjOUUsaUJBQWlCK0UsR0FBRyxDQUFDLENBQUNDLE9BQVU7d0JBQ2xEQyxJQUFJRCxLQUFLRSxtQkFBbUI7d0JBQzVCM0YsU0FBUzs0QkFBRXdFLE9BQU9pQixLQUFLRyxVQUFVOzRCQUFFdEIsT0FBT21CLEtBQUtJLFlBQVk7d0JBQUM7d0JBQzVENUYsTUFBTTs0QkFBRXVFLE9BQU9pQixLQUFLeEYsSUFBSTs0QkFBRXFFLE9BQU9tQixLQUFLeEYsSUFBSSxJQUFJO3dCQUFNO3dCQUNwREMsVUFBVXVGLEtBQUt2RixRQUFRO3dCQUN2QkMsaUJBQWlCc0YsS0FBS0ssaUJBQWlCO3dCQUN2QzFGLFVBQVVxRixLQUFLckYsUUFBUSxJQUFJO3dCQUMzQjJGLFlBQVk7d0JBQ1pDLFFBQVFWLFVBQVUsQ0FBQ0csS0FBS3JCLFNBQVMsQ0FBQztvQkFDcEM7Z0JBQ0EsTUFBTTZCLGVBQWU7dUJBQ2hCLElBQUlDLElBQUl6RixpQkFBaUIrRSxHQUFHLENBQUMsQ0FBQ0MsT0FBU0EsS0FBS3JCLFNBQVM7aUJBQ3pEO2dCQUNEMUIscUJBQXFCdUQ7Z0JBQ3JCOUQsY0FBY29EO2dCQUNkSCxRQUFRQyxHQUFHLENBQUMsZ0JBQWdCWTtZQUM5QjtRQUNGO0lBQ0YsR0FBRztRQUFDeEY7S0FBaUI7SUFDckIsc0JBQXNCO0lBRXRCLGlCQUFpQjtJQUVqQixNQUFNMEYsZ0JBQWdCLENBQUNDO1FBQ3JCLElBQUlBLE1BQU1DLElBQUksQ0FBQ0MsVUFBVSxJQUFJO1lBQzNCaEQsZ0JBQWdCOEMsTUFBTUcsSUFBSSxDQUFDYixFQUFFO1lBQzdCekQsZUFBZW1FLE1BQU1HLElBQUk7UUFDM0I7SUFDRjtJQUVBLE1BQU1DLGNBQWMsQ0FBQ0M7UUFDbkJqRCxXQUFXaUQsUUFBT0MsR0FBRztJQUN2QjtJQUVBLHdCQUF3QjtJQUN4QixNQUFNQyxjQUFjLENBQUNGO1FBQ25CLE9BQU9BLFFBQU9GLElBQUksQ0FBQ2IsRUFBRSxLQUFLckMsZUFBZSxnQkFBZ0I7SUFDM0Q7SUFFQSxNQUFNdUQsZ0JBQWdCLENBQUNDO1FBQ3JCLE1BQU1DLGVBQWUsQ0FBQ1Y7WUFDcEJBLE1BQU12QyxjQUFjO1lBQ3BCLE1BQU1rRCxjQUFjRixNQUFNTixJQUFJO1lBQzlCLE1BQU1TLGNBQWM5RSxXQUFXK0UsTUFBTSxDQUFDLENBQUN4QixPQUFTQSxTQUFTc0I7WUFDekQ1RSxjQUFjNkU7UUFDaEI7UUFFQSxNQUFNRSxhQUFhLENBQUNkO1lBQ2xCQSxNQUFNdkMsY0FBYztZQUNwQixNQUFNa0QsY0FBY0YsTUFBTU4sSUFBSTtZQUM5QjdDLHFCQUFxQjtZQUNyQkosZ0JBQWdCeUQsWUFBWXJCLEVBQUU7WUFDOUIsMkRBQTJEO1lBRTNELE1BQU1zQixjQUFjOUUsV0FBVytFLE1BQU0sQ0FBQyxDQUFDeEIsT0FBU0EsU0FBU3NCO1lBQ3pENUUsY0FBYzZFO1lBRWQsTUFBTUcsbUJBQWtCLENBQUNDLFdBQWE7b0JBQ3BDQyxtQkFBbUJaLE9BQU9GLElBQUk7b0JBQzlCZSxnQkFBZ0JGO29CQUNoQkcsb0JBQW9CO2dCQUN0QjtZQUVBLDBCQUEwQjtZQUMxQixrQ0FBa0M7WUFDbEMsb0RBQW9EO1lBQ3BELDRFQUE0RTtZQUM1RSxpQ0FBaUM7WUFDakMsNkRBQTZEO1lBRTdELCtDQUErQztZQUMvQ3RGLGVBQWU7Z0JBQ2J5RCxJQUFJcUIsWUFBWXJCLEVBQUU7Z0JBQ2xCMUYsU0FBUytHLFlBQVkvRyxPQUFPO2dCQUM1QkMsTUFBTThHLFlBQVk5RyxJQUFJO2dCQUN0QkMsVUFBVTZHLFlBQVk3RyxRQUFRO2dCQUM5QkMsaUJBQWlCNEcsWUFBWTVHLGVBQWU7Z0JBQzVDcUgscUJBQXFCVCxZQUFZNUcsZUFBZTtnQkFDaERDLFVBQVUyRyxZQUFZM0csUUFBUTtnQkFDOUIyRixZQUFZO1lBQ2Q7UUFDRjtRQUVBLHFCQUNFLDhEQUFDMEI7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNDO29CQUNDQyxTQUFTVjtvQkFDVFEsV0FBVyxHQUF5RCxPQUF0RG5GLGtCQUFrQixLQUFLLENBQUNBLGdCQUFnQixXQUFXOzhCQUVqRSw0RUFBQ3BFLDJFQUFlQTt3QkFBQzBKLE1BQU16Siw2RUFBYUE7Ozs7Ozs7Ozs7OzhCQUV0Qyw4REFBQ3VKO29CQUNDQyxTQUFTZDtvQkFDVFksV0FBVyxHQUVWLE9BRENuRixrQkFBa0IsS0FBSyxDQUFDQSxnQkFBZ0IsV0FBVyxJQUNwRDs4QkFFRCw0RUFBQ3BFLDJFQUFlQTt3QkFBQzBKLE1BQU1uSix1RUFBT0E7Ozs7Ozs7Ozs7OzhCQUVoQyw4REFBQ2lKOzhCQUNDLDRFQUFDeEosMkVBQWVBO3dCQUNkeUosU0FBU1QsZ0JBQWdCO3dCQUN6QlUsTUFBTTlJLCtFQUFhQTt3QkFDbkIySSxXQUFXLEdBRVYsT0FEQ25GLGtCQUFrQixLQUFLLENBQUNBLGdCQUFnQixXQUFXLElBQ3BEOzs7Ozs7Ozs7Ozs4QkFHTCw4REFBQ29GOzhCQUNDLDRFQUFDeEosMkVBQWVBO3dCQUNkeUosU0FBU1QsZ0JBQWdCO3dCQUN6QlUsTUFBTS9JLCtFQUFhQTt3QkFDbkI0SSxXQUFXLEdBRVYsT0FEQ25GLGtCQUFrQixLQUFLLENBQUNBLGdCQUFnQixXQUFXLElBQ3BEOzs7Ozs7Ozs7Ozs4QkFHTCw4REFBQ29GOzhCQUNDLDRFQUFDeEosMkVBQWVBO3dCQUNkeUosU0FBU1QsZ0JBQWdCO3dCQUN6QlUsTUFBTXRKLDZFQUFhQTt3QkFDbkJtSixXQUFXLEdBRVYsT0FEQ25GLGtCQUFrQixLQUFLLENBQUNBLGdCQUFnQixXQUFXLElBQ3BEOzs7Ozs7Ozs7Ozs7Ozs7OztJQUtYO0lBRUEsa0NBQWtDO0lBQ2xDLE1BQU11RixhQUFhO1FBQ2pCO1lBQ0VDLFlBQVk7WUFDWkMsT0FBTztZQUNQQyxNQUFNO1lBQ05DLGFBQWE7UUFDZjtRQUNBO1lBQ0VILFlBQVk7WUFDWkMsT0FBTztZQUNQQyxNQUFNO1lBQ05DLGFBQWE7UUFDZjtRQUNBO1lBQ0VILFlBQVk7WUFDWkMsT0FBTztZQUNQQyxNQUFNO1lBQ05DLGFBQWE7UUFDZjtRQUNBO1lBQ0VILFlBQVk7WUFDWkMsT0FBTztZQUNQQyxNQUFNO1lBQ05DLGFBQWE7UUFDZjtRQUNBO1lBQ0VILFlBQVk7WUFDWkMsT0FBTztZQUNQQyxNQUFNO1lBQ05DLGFBQWE7UUFDZjtRQUNBO1lBQ0VILFlBQVk7WUFDWkksY0FBY3ZCO1lBQ2RzQixhQUFhO1lBQ2JELE1BQU07WUFDTkcsV0FBVztnQkFBRUMsZ0JBQWdCO2dCQUFVQyxjQUFjO1lBQU87UUFNOUQ7UUFDQTtZQUNFUCxZQUFZO1lBQ1pDLE9BQU87WUFDUE8sTUFBTWhHLGtCQUFrQixLQUFLLENBQUNBO1lBQzlCNEYsY0FBY25KLDhFQUFxQkE7WUFDbkNvSixXQUFXLElBQU87b0JBQUVDLGdCQUFnQjtnQkFBUztZQUM3Q0osTUFBTTtZQUNOTyxjQUFjO2dCQUNaQyxRQUFRO29CQUFDO29CQUFrQjtvQkFBWTtvQkFBWTtvQkFBVztpQkFBUTtZQUN4RTtRQUNGO0tBQ0Q7SUFDRCxvQkFBb0I7SUFFcEIsZUFBZTtJQUNmLFNBQVNDO1FBQ1AsSUFBSUMsZ0JBQWdCekssMERBQVNBLENBQUN5SyxhQUFhO1FBQzNDLE9BQU9DLE1BQU0sR0FBaUIsT0FBZEQsZUFBYyxpQ0FBK0I7WUFDM0RFLFFBQVE7WUFDUkMsU0FBUztnQkFDUCxnQkFBZ0I7WUFDbEI7WUFDQUMsYUFBYTtRQUNmLEdBQ0dDLElBQUksQ0FBQyxPQUFPQztZQUNYLElBQUlBLElBQUlqRCxNQUFNLElBQUksS0FBSztnQkFDckI7WUFDRjtZQUNBLElBQUlpRCxJQUFJakQsTUFBTSxLQUFLLEtBQUs7Z0JBQ3RCLE9BQU9pRCxJQUFJQyxJQUFJO1lBQ2pCO1lBQ0EsSUFBSUQsSUFBSWpELE1BQU0sS0FBSyxLQUFLO2dCQUN0QmpJLGlEQUFLQSxDQUFDb0wsS0FBSyxDQUNUO1lBRUosT0FBTyxJQUFJRixJQUFJakQsTUFBTSxLQUFLLEtBQUs7Z0JBQzdCakksaURBQUtBLENBQUNvTCxLQUFLLENBQUM7Z0JBQ1pyRixXQUFXO29CQUNULE1BQU1qRiw2REFBTUE7b0JBQ1ptRCxPQUFPb0gsSUFBSSxDQUFDO2dCQUNkLEdBQUc7WUFDTDtZQUNBLE1BQU0sSUFBSUMsTUFBTTtRQUNsQixHQUNDQyxLQUFLLENBQUMsQ0FBQ0g7WUFDTi9ELFFBQVErRCxLQUFLLENBQUNBO1lBQ2RwTCxpREFBS0EsQ0FBQ29MLEtBQUssQ0FBQyw2QkFBMkMsT0FBZEEsTUFBTUksT0FBTztZQUN0RCxNQUFNSjtRQUNSO0lBQ0o7SUFDQSxTQUFTSztRQUNQLElBQUliLGdCQUFnQnpLLDBEQUFTQSxDQUFDeUssYUFBYTtRQUMzQyxPQUFPQyxNQUFNLEdBQWlCLE9BQWRELGVBQWMsMEJBQXdCO1lBQ3BERSxRQUFRO1lBQ1JDLFNBQVM7Z0JBQ1AsZ0JBQWdCO1lBQ2xCO1lBQ0FDLGFBQWE7UUFDZixHQUFHQyxJQUFJLENBQUMsT0FBT0M7WUFDYixJQUFJQSxJQUFJakQsTUFBTSxJQUFJLEtBQUs7Z0JBQ3JCO1lBQ0Y7WUFDQSxJQUFJaUQsSUFBSWpELE1BQU0sS0FBSyxLQUFLO2dCQUN0QixPQUFPaUQsSUFBSUMsSUFBSTtZQUNqQjtZQUNBLElBQUlELElBQUlqRCxNQUFNLEtBQUssS0FBSztnQkFDdEJqSSxpREFBS0EsQ0FBQ29MLEtBQUssQ0FDVDtZQUVKLE9BQU8sSUFBSUYsSUFBSWpELE1BQU0sS0FBSyxLQUFLO2dCQUM3QmpJLGlEQUFLQSxDQUFDb0wsS0FBSyxDQUFDO2dCQUNackYsV0FBVztvQkFDVCxNQUFNakYsNkRBQU1BO29CQUNabUQsT0FBT29ILElBQUksQ0FBQztnQkFDZCxHQUFHO1lBQ0w7WUFDQSxNQUFNLElBQUlDLE1BQU07UUFDbEI7SUFDRjtJQUNBLGtCQUFrQjtJQUNsQjNMLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTStMLGFBQWE7WUFDakIsSUFBSTtnQkFDRixNQUFNdkksV0FBVyxNQUFNc0k7Z0JBQ3ZCLE1BQU1FLGlCQUFpQnhJLHFCQUFBQSwrQkFBQUEsU0FBVXNFLEdBQUcsQ0FBQyxDQUFDOUQsT0FBVTt3QkFDOUM4QyxPQUFPOUMsS0FBS2dFLEVBQUU7d0JBQ2RwQixPQUFPNUMsS0FBS3dDLElBQUk7b0JBQ2xCO2dCQUNBL0MsWUFBWXVJO2dCQUNaN0gsV0FBVztZQUNiLEVBQUUsT0FBT3NILE9BQU87Z0JBQ2QvRCxRQUFRK0QsS0FBSyxDQUFDLHdCQUF3QkE7WUFDeEM7UUFDRjtRQUNBLE1BQU1RLGdCQUFnQjtZQUNwQixJQUFJO2dCQUNGLE1BQU1DLGVBQWUsTUFBTWxCO2dCQUMzQixNQUFNbUIsMEJBQTBCRCxhQUFhM0MsTUFBTSxDQUNqRCxDQUFDakgsVUFBWSxDQUFDQSxRQUFROEosZUFBZTtnQkFFdkMsTUFBTUMsb0JBQW9CRixvQ0FBQUEsOENBQUFBLHdCQUF5QnJFLEdBQUcsQ0FBQyxDQUFDeEYsVUFBYTt3QkFDbkV3RSxPQUFPeEUsUUFBUWdLLFNBQVM7d0JBQ3hCMUYsT0FBT3RFLFFBQVFpSyxXQUFXO3dCQUMxQkMsZUFBZWxLLFFBQVFtSyxjQUFjLElBQUluSyxRQUFRa0ssYUFBYTt3QkFDOURFLE9BQU9wSyxRQUFRcUssY0FBYyxHQUN6QnJLLFFBQVFxSyxjQUFjLENBQUNDLEtBQUssQ0FBQyxNQUFNOUUsR0FBRyxDQUFDLENBQUN2RixPQUFVO2dDQUNoRHVFLE9BQU92RSxLQUFLc0ssV0FBVyxHQUFHQyxPQUFPLENBQUMsUUFBUTtnQ0FDMUNsRyxPQUFPckU7NEJBQ1QsTUFDQSxFQUFFO3dCQUNOd0ssYUFBYXpLLFFBQVEwSyxXQUFXO3dCQUNoQ0MsaUJBQWlCM0ssUUFBUTRLLGVBQWU7d0JBQ3hDQyxhQUFhN0ssUUFBUThLLFdBQVc7d0JBQ2hDQyxnQkFBZ0IvSyxRQUFRK0ssY0FBYztvQkFDeEM7Z0JBRUFoSixnQkFBZ0JnSTtZQUNsQixFQUFFLE9BQU9aLE9BQU87Z0JBQ2QvRCxRQUFRK0QsS0FBSyxDQUFDLDRCQUE0QkE7WUFDNUM7UUFDRjtRQUVBUTtRQUNBRjtRQUNBNUgsV0FBVztJQUNiLEdBQUcsRUFBRTtJQUVMLGtCQUFrQjtJQUNsQixrRUFBa0U7SUFDbEUsTUFBTW1KLFdBQVc7UUFDZixNQUFNQyxZQUFZLENBQUM7UUFFbkIsSUFBSSxDQUFDdkosTUFBTXVKLFVBQVV2SixJQUFJLEdBQUc7UUFDNUIsSUFBSSxDQUFDSixjQUFjMkosVUFBVTNKLFlBQVksR0FBRztRQUM1QyxJQUFJLENBQUNZLGNBQWNBLFdBQVdpQyxNQUFNLEtBQUssR0FBRztZQUMxQzhHLFVBQVUvSSxVQUFVLEdBQUc7UUFDekI7UUFFQSx3Q0FBd0M7UUFDeENVLFVBQVVxSTtRQUVWLE1BQU1DLFlBQVlDLE9BQU9DLElBQUksQ0FBQ0gsV0FBVzlHLE1BQU0sR0FBRztRQUNsRCxJQUFJK0csV0FBVztZQUNiLE1BQU1HLGdCQUFnQkYsT0FBTzFDLE1BQU0sQ0FBQ3dDO1lBRXBDLG1CQUFtQjtZQUNuQiwrQkFBK0I7WUFDL0IsdUNBQXVDO1lBQ3ZDLG1CQUFtQjtZQUNuQixNQUFNO1lBQ04sdUNBQXVDO1lBQ3ZDLE1BQU1LLGVBQWVILE9BQU9DLElBQUksQ0FBQ0gsV0FBVzlHLE1BQU07WUFDbEQsSUFBSW1ILGVBQWUsR0FBRztnQkFDcEJ2TixpREFBS0EsQ0FBQ29MLEtBQUssQ0FBQztZQUNkLE9BQU8sSUFBSW1DLGdCQUFnQixLQUFLTCxVQUFVL0ksVUFBVSxFQUFFO2dCQUNwRG5FLGlEQUFLQSxDQUFDb0wsS0FBSyxDQUFDO1lBQ2Q7WUFDQSxPQUFPO1FBQ1Q7UUFFQSxPQUFPO0lBQ1Q7SUFDQSxNQUFNb0MsZ0JBQWdCLENBQUMvRztRQUNyQjdDLFFBQVE2QztRQUVSLDZDQUE2QztRQUM3QzVCLFVBQVUsQ0FBQzRJO1lBQ1QsTUFBTUMsVUFBVTtnQkFBRSxHQUFHRCxJQUFJO1lBQUM7WUFDMUIsSUFBSWhILE9BQU87Z0JBQ1QsT0FBT2lILFFBQVEvSixJQUFJO1lBQ3JCO1lBQ0EsT0FBTytKO1FBQ1Q7SUFDRjtJQUVBLCtCQUErQjtJQUMvQixNQUFNQyx1QkFBdUIsQ0FBQ0M7UUFDNUIsaUNBQWlDO1FBRWpDL0ksVUFBVSxDQUFDNEk7WUFDVCxNQUFNQyxVQUFVO2dCQUFFLEdBQUdELElBQUk7WUFBQztZQUMxQixJQUFJRyxjQUFjO2dCQUNoQixPQUFPRixRQUFRbkssWUFBWTtZQUM3QjtZQUNBLE9BQU9tSztRQUNUO1FBRUEsSUFBSSxDQUFDRSxjQUFjO1lBQ2pCckosZUFBZTtZQUNmO1FBQ0Y7UUFFQSxNQUFNc0osUUFBUSxJQUFJbEg7UUFDbEIsTUFBTW1ILFdBQVcsSUFBSW5ILEtBQUtpSDtRQUMxQixNQUFNRyxrQkFBa0IsSUFBSXBIO1FBQzVCb0gsZ0JBQWdCQyxPQUFPLENBQUNILE1BQU1JLE9BQU8sS0FBSztRQUUxQyxJQUFJSCxXQUFXQyxpQkFBaUI7WUFDOUJ4SixlQUNFO1FBRUosT0FBTztZQUNMQSxlQUFlO1FBQ2pCO0lBQ0Y7SUFFQSxtQkFBbUI7SUFFbkIsTUFBTTJKLGdCQUFnQixDQUFDdk07UUFDckIsT0FBT0EsTUFBTXdNLElBQUk7SUFDbkI7SUFFQSxNQUFNQyxxQkFBcUIsQ0FBQzVGO1FBQzFCLElBQUlBLE1BQU07WUFDUm5ELHVCQUF1QjtRQUN6QixPQUFPO1lBQ0xBLHVCQUF1QjtRQUN6QjtJQUNGO0lBRUEsTUFBTWdKLGtCQUFrQixDQUFDeEk7UUFDdkIsc0JBQXNCO1FBQ3RCLElBQUlILG1CQUFtQjtZQUNyQlQsYUFBYTtZQUNibkMsVUFBVSxPQUFPLDRCQUE0QjtRQUMvQyxPQUFPO1lBQ0x3TCxnQkFBZ0J6STtRQUNsQjtJQUNGO0lBRUEsTUFBTXlJLGtCQUFrQixDQUFDekk7WUF1Q3JCakQsc0JBQ0FBLDRCQUFBQTtRQXZDRnlFLFFBQVFDLEdBQUcsQ0FBQztRQUNaekMsVUFBVSxDQUFDNEk7WUFDVCxNQUFNQyxVQUFVO2dCQUFFLEdBQUdELElBQUk7WUFBQztZQUMxQixJQUFJNUgsR0FBRztnQkFDTCxPQUFPNkgsUUFBUXZKLFVBQVU7WUFDM0I7WUFDQSxPQUFPdUo7UUFDVDtRQUVBLElBQUksQ0FBQzdILEdBQUc7WUFDTndCLFFBQVFDLEdBQUcsQ0FBQztZQUNaL0MsZUFBZTtZQUNmO1FBQ0Y7UUFDQXNCLEVBQUVDLGNBQWM7UUFDaEIsSUFBSXlJLFFBQVE7UUFFWixJQUFJLENBQUMzTCxZQUFZWCxPQUFPLEVBQUU7WUFDeEJvRixRQUFRQyxHQUFHLENBQUM7WUFDWmlIO1FBQ0EsOENBQThDO1FBQ2hELE9BQU87UUFDTCx1QkFBdUI7UUFDekI7UUFFQSxJQUFJLENBQUMzTCxZQUFZVCxRQUFRLElBQUlTLFlBQVlULFFBQVEsR0FBRyxHQUFHO1lBQ3JEa0YsUUFBUUMsR0FBRyxDQUFDO1lBQ1ppSDtRQUNBLHFEQUFxRDtRQUN2RCxPQUFPO1lBQ0wsSUFBSTNMLFlBQVlULFFBQVEsR0FBRyxLQUFLUyxZQUFZUixlQUFlLEVBQUU7Z0JBQzNELGlDQUFpQztnQkFDakM4QixlQUFlLENBQUN1SixPQUFVO3dCQUFFLEdBQUdBLElBQUk7d0JBQUVyTCxpQkFBaUI7b0JBQUc7WUFDM0Q7UUFDQSx3QkFBd0I7UUFDMUI7UUFFQSxJQUNFUSxFQUFBQSx1QkFBQUEsWUFBWVgsT0FBTyxjQUFuQlcsMkNBQUFBLHFCQUFxQnVKLGFBQWEsT0FDbEN2Six3QkFBQUEsWUFBWVgsT0FBTyxjQUFuQlcsNkNBQUFBLDZCQUFBQSxzQkFBcUJ5SixLQUFLLGNBQTFCekosaURBQUFBLDJCQUE0QndELE1BQU0sS0FDbEMsQ0FBQ3hELFlBQVlWLElBQUksRUFDakI7WUFDQW1GLFFBQVFDLEdBQUcsQ0FBQztZQUNaaUg7UUFDQSxnQ0FBZ0M7UUFDbEM7UUFFQSxJQUFJQSxRQUFRLEdBQUc7WUFDYnZPLGlEQUFLQSxDQUFDb0wsS0FBSyxDQUNUO1lBRUY7UUFDRjtRQUNBLDBCQUEwQjtRQUMxQixrQ0FBa0M7UUFDbEMsZ0RBQWdEO1FBQ2hELDZFQUE2RTtRQUM3RSxPQUFPO1FBQ1AsaUNBQWlDO1FBQ2pDLDJCQUEyQjtRQUMzQixXQUFXO1FBQ1gsb0JBQW9CO1FBQ3BCLHdFQUF3RTtRQUV4RSxJQUFJO1FBQ0osSUFBSTlGLGNBQWM7WUFDaEIseUNBQXlDO1lBQ3pDbEIsY0FBYzttQkFDVEQ7Z0JBQ0g7b0JBQ0UsR0FBR3ZCLFdBQVc7b0JBQ2QrRSxJQUFJckM7b0JBQ0pyRCxTQUFTVyxZQUFZWCxPQUFPLEdBQUc7d0JBQUUsR0FBR1csWUFBWVgsT0FBTztvQkFBQyxJQUFJO29CQUM1REMsTUFBTVUsWUFBWVYsSUFBSSxHQUFHO3dCQUFFLEdBQUdVLFlBQVlWLElBQUk7b0JBQUMsSUFBSTtnQkFDckQ7YUFDRDtZQUNEcUQsZ0JBQWdCO1FBQ2xCLE9BQU87WUFDTCxlQUFlO1lBQ2ZuQixjQUFjO21CQUNURDtnQkFDSDtvQkFBRSxHQUFHdkIsV0FBVztvQkFBRStFLElBQUksT0FBa0IsT0FBWGhCLEtBQUs2SCxHQUFHO2dCQUFLO2FBQzNDO1FBQ0g7UUFFQSxxQkFBcUI7UUFDckJ0SyxlQUFlO1lBQUUsR0FBR2xDLFNBQVM7UUFBQztRQUM5QnVELGdCQUFnQjtRQUNoQkkscUJBQXFCO0lBQ3ZCO0lBRUEsOEJBQThCO0lBQzlCLE1BQU04SSwwQkFBMEIsQ0FBQ3hFLE9BQU94RDtRQUN0QyxJQUFJd0QsVUFBVSxXQUFXO1lBQ3ZCLDBDQUEwQztZQUMxQywwQ0FBMEM7WUFDMUMvRixlQUFlLENBQUN1SixPQUFVO29CQUN4QixHQUFHQSxJQUFJO29CQUNQLENBQUN4RCxNQUFNLEVBQUV4RDtvQkFDVHZFLE1BQU07b0JBQ05DLFVBQVU7b0JBQ1ZDLGlCQUFpQjtvQkFDakJxSCxxQkFBcUI7b0JBQ3JCcEgsVUFBVTtnQkFDWjtRQUNGLE9BQU8sSUFBSTRILFVBQVUsY0FBY3hELFFBQVEsR0FBRztZQUM1Q3ZDLGVBQWUsQ0FBQ3VKLE9BQVU7b0JBQ3hCLEdBQUdBLElBQUk7b0JBQ1AsQ0FBQ3hELE1BQU0sRUFBRXhEO29CQUNUckUsaUJBQWlCO2dCQUNuQjtRQUNGLE9BQU87WUFDTDhCLGVBQWUsQ0FBQ3VKLE9BQVU7b0JBQUUsR0FBR0EsSUFBSTtvQkFBRSxDQUFDeEQsTUFBTSxFQUFFeEQ7Z0JBQU07UUFDdEQ7SUFDRjtJQUVBLGNBQWM7SUFDZCxNQUFNaUksZUFBZSxDQUFDQztRQUNwQixJQUFJQSxhQUFhLEtBQUssQ0FBQzFCLFlBQVk7WUFDakM7UUFDRjtRQUNBLG1GQUFtRjtRQUNuRm5KLFdBQVc7UUFDWCxJQUFJOEcsZ0JBQWdCekssMERBQVNBLENBQUN5SyxhQUFhO1FBRTNDLHFDQUFxQztRQUNyQyxNQUFNdkgsUUFBUWMsV0FBV3NELEdBQUcsQ0FBQyxDQUFDQztnQkFJakJBLGVBQ0VBLGdCQUF1QkEsZ0JBQzlCQTttQkFOZ0M7Z0JBQ3RDa0gsZUFBZWxILEtBQUtrSCxhQUFhO2dCQUNqQyw2RkFBNkY7Z0JBRTdGQyxTQUFTLEdBQUVuSCxnQkFBQUEsS0FBS3pGLE9BQU8sY0FBWnlGLG9DQUFBQSxjQUFjakIsS0FBSztnQkFDOUJ5RixhQUFheEUsRUFBQUEsaUJBQUFBLEtBQUt6RixPQUFPLGNBQVp5RixxQ0FBQUEsZUFBY25CLEtBQUssT0FBSW1CLGlCQUFBQSxLQUFLekYsT0FBTyxjQUFaeUYscUNBQUFBLGVBQWNqQixLQUFLO2dCQUN2RHFJLE1BQU1wSCxFQUFBQSxhQUFBQSxLQUFLeEYsSUFBSSxjQUFUd0YsaUNBQUFBLFdBQVduQixLQUFLLEtBQUk7Z0JBQzFCd0ksVUFBVUMsT0FBT3RILEtBQUt2RixRQUFRLEtBQUs7Z0JBQ25DOE0saUJBQWlCdkgsS0FBS3RGLGVBQWUsSUFBSTtnQkFDekM4TSxVQUFVeEgsS0FBS3JGLFFBQVEsSUFBSTtZQUM3Qjs7UUFFQSxNQUFNOE0sYUFBYTtZQUNqQkMsV0FBV1Q7WUFDWFUsZUFBZSxFQUFFOU0scUJBQUFBLCtCQUFBQSxTQUFVK00sT0FBTztZQUNsQ0MsY0FBYzVMLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTThDLEtBQUssS0FBSTtZQUM3QitJLGNBQWNqTTtZQUNka00sVUFBVWxOLENBQUFBLHFCQUFBQSwrQkFBQUEsU0FBVW1OLEtBQUssS0FBSTtZQUM3QnJNLE9BQU9BO1lBQ1BzTSxnQkFBZ0IxTTtRQUNsQjtRQUVBLHFGQUFxRjtRQUVyRixJQUFJO1lBQ0Y0SCxNQUFNLEdBQWlCLE9BQWRELGVBQWMseUNBQXVDO2dCQUM1REUsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxhQUFhO2dCQUNiNEUsTUFBTUMsS0FBS0MsU0FBUyxDQUFDWDtZQUN2QixHQUNHbEUsSUFBSSxDQUFDLENBQUNDO2dCQUNMLElBQUlBLElBQUlqRCxNQUFNLEtBQUssS0FBSztvQkFDdEIsT0FBT2lELElBQUlDLElBQUk7Z0JBQ2pCLE9BQU8sSUFBSUQsSUFBSWpELE1BQU0sS0FBSyxLQUFLO29CQUM3QmpJLGlEQUFLQSxDQUFDb0wsS0FBSyxDQUFDO29CQUNackYsV0FBVzt3QkFDVCxNQUFNakYsNkRBQU1BO3dCQUNaLE1BQU1pUCxjQUFjLG1CQUVsQixPQUZxQ0MsbUJBQ3JDQyxPQUFPQyxRQUFRLENBQUNDLFFBQVE7d0JBRTFCbE0sT0FBT29ILElBQUksQ0FBQzBFO29CQUNkLEdBQUc7b0JBQ0gsT0FBTztnQkFDVCxPQUFPO29CQUNML1AsaURBQUtBLENBQUNvTCxLQUFLLENBQUM7b0JBQ1p0SCxXQUFXO2dCQUNiO1lBQ0YsR0FDQ21ILElBQUksQ0FBQyxDQUFDRTtnQkFDTCxJQUFJQSxRQUFRQSxLQUFLaUYsR0FBRyxFQUFFO29CQUNwQnBRLGlEQUFLQSxDQUFDcVEsT0FBTyxDQUFDbEYsS0FBS2lGLEdBQUcsRUFBRTt3QkFDdEJFLFVBQVU7b0JBQ1o7b0JBQ0F2SyxXQUFXO3dCQUNUOUIsT0FBT3dJLE9BQU8sQ0FBQztvQkFDakIsR0FBRztnQkFDTDtZQUNGO1FBQ0osRUFBRSxPQUFPckIsT0FBTztZQUNkL0QsUUFBUUMsR0FBRyxDQUFDLGdDQUFnQzhEO1FBQzlDO0lBQ0EscUJBQXFCO0lBQ3JCLHFCQUFxQjtJQUNyQix1QkFBdUI7SUFDdkIseUNBQXlDO0lBQ3pDLG9DQUFvQztJQUNwQyxZQUFZO0lBQ2Q7SUFFQSw2QkFBNkI7SUFDN0IsTUFBTW1GLDBCQUEwQixDQUM5QkMsaUJBQ0FDLGtCQUNBQztRQUVBLE1BQU1uSixhQUFhO1lBQ2pCLEdBQUc7WUFDSCxHQUFHO1lBQ0gsR0FBRztZQUNILEdBQUc7WUFDSCxHQUFHO1FBQ0w7UUFDQSxNQUFNb0osY0FBY3hNLFdBQVcrRSxNQUFNLENBQ25DLENBQUN4QixPQUFTQSxLQUFLTyxNQUFNLElBQUlWLFVBQVUsQ0FBQ2tKLGlCQUFpQjtRQUd2RCxJQUFJRSxZQUFZdkssTUFBTSxLQUFLLEdBQUc7WUFDNUJwRyxpREFBS0EsQ0FBQzRRLE9BQU8sQ0FBQztZQUNkO1FBQ0Y7UUFFQTlNLFdBQVc7UUFFWCxJQUFJME0sbUJBQW1CLEdBQUc7WUFDeEIsSUFBSXRMLG9CQUFvQjtnQkFDdEJHLHVCQUF1QjtZQUN6QixPQUFPO2dCQUNMZ0MsUUFBUUMsR0FBRyxDQUFDO2dCQUNaakMsdUJBQXVCO2dCQUN2QjtZQUNGO1FBQ0Y7UUFFQSxxRUFBcUU7UUFDckUsTUFBTXdMLGlCQUFpQkYsWUFBWWxKLEdBQUcsQ0FBQyxDQUFDQztZQUN0QyxNQUFNeUgsYUFBYTtnQkFDakIyQixZQUFZcEosS0FBS0MsRUFBRTtnQkFDbkJ1RSxhQUFheEUsS0FBS3pGLE9BQU8sQ0FBQ3NFLEtBQUs7Z0JBQy9CdUksTUFBTXBILEtBQUt4RixJQUFJLENBQUNxRSxLQUFLO2dCQUNyQndJLFVBQVVySCxLQUFLdkYsUUFBUTtnQkFDdkI4TSxpQkFBaUJ2SCxLQUFLdEYsZUFBZTtnQkFDckM4TSxVQUFVeEgsS0FBS3JGLFFBQVE7Z0JBQ3ZCME8sZUFBZXJKLEtBQUtDLEVBQUU7Z0JBQ3RCcUosMEJBQTBCTjtnQkFDMUJySyxXQUFXbUs7Z0JBQ1huQixpQkFBaUI5TSxTQUFTK00sT0FBTztnQkFDakMyQixjQUFjLEVBQUUxTyxxQkFBQUEsK0JBQUFBLFNBQVVtTixLQUFLO2dCQUMvQkMsZ0JBQWdCMU07Z0JBQ2hCaUMsb0JBQW9CQTtZQUN0QjtZQUNBbUMsUUFBUUMsR0FBRyxDQUFDLGNBQWM2SCxhQUFhLDhCQUE4QjtZQUNyRSxPQUFPdEUsTUFDTCxHQUEwRW5ELE9BQXZFdkgsMERBQVNBLENBQUN5SyxhQUFhLEVBQUMsaURBQXVELE9BQVJsRCxLQUFLQyxFQUFFLEdBQ2pGO2dCQUNFbUQsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxhQUFhO2dCQUNiNEUsTUFBTUMsS0FBS0MsU0FBUyxDQUFDWDtZQUN2QixHQUNBbEUsSUFBSSxDQUFDLE9BQU9DO2dCQUNaLElBQUlBLElBQUlqRCxNQUFNLEtBQUssS0FBSztvQkFDdEIsT0FBT2lELElBQUlDLElBQUk7Z0JBQ2pCLE9BQU8sSUFBSUQsSUFBSWpELE1BQU0sS0FBSyxLQUFLO29CQUM3QmpJLGlEQUFLQSxDQUFDb0wsS0FBSyxDQUFDO29CQUNackYsV0FBVzt3QkFDVCxNQUFNakYsNkRBQU1BO3dCQUNaLE1BQU1pUCxjQUFjLG1CQUVsQixPQUZxQ0MsbUJBQ3JDQyxPQUFPQyxRQUFRLENBQUNDLFFBQVE7d0JBRTFCbE0sT0FBT29ILElBQUksQ0FBQzBFO29CQUNkLEdBQUc7b0JBQ0gsTUFBTSxJQUFJekUsTUFBTTtnQkFDbEIsT0FBTztvQkFDTCxNQUFNNEYsWUFBWSxNQUFNaEcsSUFBSWlHLElBQUk7b0JBQ2hDOUosUUFBUStELEtBQUssQ0FBQyxrQkFBa0I4RjtvQkFDaENsUixpREFBS0EsQ0FBQ29MLEtBQUssQ0FBQztvQkFDWixNQUFNLElBQUlFLE1BQU07Z0JBQ2xCO1lBQ0Y7UUFDRjtRQUVBOEYsUUFBUUMsR0FBRyxDQUFDUixnQkFDVDVGLElBQUksQ0FBQyxDQUFDcUc7WUFDTCxNQUFNQyxlQUFlRCxRQUFRcEksTUFBTSxDQUFDLENBQUNzSSxTQUFXQSxtQkFBQUEsNkJBQUFBLE9BQVFwQixHQUFHLEVBQUVoSyxNQUFNO1lBQ25FLElBQUltTCxlQUFlLEdBQUc7Z0JBQ3BCdlIsaURBQUtBLENBQUNxUSxPQUFPLENBQUMsd0JBQXFDLE9BQWJrQixjQUFhLGFBQVc7b0JBQzVEakIsVUFBVTtnQkFDWjtnQkFDQXZLLFdBQVc7b0JBQ1Q5QixPQUFPd0ksT0FBTyxDQUFDO2dCQUNqQixHQUFHO1lBQ0wsT0FBTztnQkFDTHpNLGlEQUFLQSxDQUFDb0wsS0FBSyxDQUFDO2dCQUNabkgsT0FBT29ILElBQUksQ0FBQztZQUNkO1FBQ0YsR0FDQ0UsS0FBSyxDQUFDLENBQUNIO1lBQ04vRCxRQUFRK0QsS0FBSyxDQUFDLHlCQUF5QkE7WUFDdkNwTCxpREFBS0EsQ0FBQ29MLEtBQUssQ0FBQztRQUNkLEdBQ0NxRyxPQUFPLENBQUM7WUFDUDNOLFdBQVc7UUFDYjtJQUNKO0lBQ0EsZ0NBQWdDO0lBRWhDLGlCQUFpQjtJQUNqQixNQUFNNE4sZUFBZSxDQUFDckw7UUFDcEIsSUFBSSxDQUFDNEcsWUFBWTtZQUNmO1FBQ0Y7UUFDQSxxQkFBcUI7UUFDckJuSixXQUFXO1FBQ1gsSUFBSThHLGdCQUFnQnpLLDBEQUFTQSxDQUFDeUssYUFBYTtRQUUzQ3ZELFFBQVFDLEdBQUcsQ0FBQyxjQUFjbkQ7UUFDMUIsTUFBTWQsUUFBUWMsV0FBV3NELEdBQUcsQ0FBQyxDQUFDQztnQkFPRUEsZUFDakJBLGdCQUF1QkEsZ0JBQzlCQSxZQUdKQTttQkFab0M7Z0JBQ3RDLDBCQUEwQjtnQkFDMUJxSixlQUNFLE9BQU9ySixLQUFLQyxFQUFFLEtBQUssWUFBWUQsS0FBS0MsRUFBRSxDQUFDZ0ssVUFBVSxDQUFDLFVBQzlDLE9BQ0FqSyxLQUFLQyxFQUFFO2dCQUVia0gsV0FBV25ILEtBQUtHLFVBQVUsTUFBSUgsZ0JBQUFBLEtBQUt6RixPQUFPLGNBQVp5RixvQ0FBQUEsY0FBY2pCLEtBQUs7Z0JBQ2pEeUYsYUFBYXhFLEVBQUFBLGlCQUFBQSxLQUFLekYsT0FBTyxjQUFaeUYscUNBQUFBLGVBQWNuQixLQUFLLE9BQUltQixpQkFBQUEsS0FBS3pGLE9BQU8sY0FBWnlGLHFDQUFBQSxlQUFjakIsS0FBSztnQkFDdkRxSSxNQUFNcEgsRUFBQUEsYUFBQUEsS0FBS3hGLElBQUksY0FBVHdGLGlDQUFBQSxXQUFXbkIsS0FBSyxLQUFJO2dCQUMxQiwwREFBMEQ7Z0JBQzFEcUwsUUFDRWxLLEVBQUFBLGNBQUFBLEtBQUt4RixJQUFJLGNBQVR3RixrQ0FBQUEsWUFBV2pCLEtBQUssS0FBSSxDQUFDb0wsTUFBTTdDLE9BQU90SCxLQUFLeEYsSUFBSSxDQUFDdUUsS0FBSyxLQUM3Q3VJLE9BQU90SCxLQUFLeEYsSUFBSSxDQUFDdUUsS0FBSyxJQUN0QjtnQkFDTnNJLFVBQVVDLE9BQU90SCxLQUFLdkYsUUFBUSxLQUFLO2dCQUNuQzhNLGlCQUFpQnZILEtBQUt0RixlQUFlLElBQUk7Z0JBQ3pDOE0sVUFBVXhILEtBQUtyRixRQUFRLElBQUk7WUFDN0I7O1FBRUEsTUFBTThNLGFBQWE7WUFDakIyQyxXQUFXblA7WUFDWDBELFdBQVdBO1lBQ1gwTCxpQkFBaUJ4UCxTQUFTK00sT0FBTztZQUNqQ0csVUFBVWxOLENBQUFBLHFCQUFBQSwrQkFBQUEsU0FBVW1OLEtBQUssS0FBSTtZQUM3QnNDLGNBQWNyTyxLQUFLOEMsS0FBSztZQUN4QmxELGNBQWNBO1lBQ2RGLE9BQU9BO1lBQ1BxTixTQUFTeEw7WUFDVHlLLGdCQUFnQjFNO1FBQ2xCO1FBQ0FvRSxRQUFRQyxHQUFHLENBQ1Qsc0VBQ0E2SDtRQUdGLElBQUk7WUFDRnRFLE1BQU0sR0FBaUIsT0FBZEQsZUFBYyx5Q0FBdUM7Z0JBQzVERSxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLGFBQWE7Z0JBQ2I0RSxNQUFNQyxLQUFLQyxTQUFTLENBQUNYO1lBQ3ZCLEdBQ0dsRSxJQUFJLENBQUMsQ0FBQ0M7Z0JBQ0wsSUFBSUEsSUFBSWpELE1BQU0sS0FBSyxLQUFLO29CQUN0QixPQUFPaUQsSUFBSUMsSUFBSTtnQkFDakIsT0FBTyxJQUFJRCxJQUFJakQsTUFBTSxLQUFLLEtBQUs7b0JBQzdCakksaURBQUtBLENBQUNvTCxLQUFLLENBQUM7b0JBQ1pyRixXQUFXO3dCQUNULE1BQU1qRiw2REFBTUE7d0JBQ1osTUFBTWlQLGNBQWMsbUJBRWxCLE9BRnFDQyxtQkFDckNDLE9BQU9DLFFBQVEsQ0FBQ0MsUUFBUTt3QkFFMUJsTSxPQUFPb0gsSUFBSSxDQUFDMEU7b0JBQ2QsR0FBRztvQkFDSCxPQUFPO2dCQUNULE9BQU87b0JBQ0wvUCxpREFBS0EsQ0FBQ29MLEtBQUssQ0FBQztvQkFDWnRILFdBQVc7Z0JBQ2I7WUFDRixHQUNDbUgsSUFBSSxDQUFDLENBQUNFO2dCQUNMLElBQUlBLFFBQVFBLEtBQUtpRixHQUFHLEVBQUU7b0JBQ3BCcFEsaURBQUtBLENBQUNxUSxPQUFPLENBQUNsRixLQUFLaUYsR0FBRyxFQUFFO3dCQUN0QkUsVUFBVTtvQkFDWjtvQkFDQXZLLFdBQVc7d0JBQ1Q5QixPQUFPd0ksT0FBTyxDQUFDO29CQUNqQixHQUFHO2dCQUNMO1lBQ0Y7UUFDSixFQUFFLE9BQU9yQixPQUFPO1lBQ2QvRCxRQUFRQyxHQUFHLENBQUMsZ0NBQWdDOEQ7UUFDOUM7UUFDQXRILFdBQVc7UUFDWGlDLFdBQVc7WUFDVGpDLFdBQVc7WUFDWDlELGlEQUFLQSxDQUFDcVEsT0FBTyxDQUFDO1lBQ2RwTSxPQUFPb0gsSUFBSSxDQUFDO1FBQ2QsR0FBRztJQUNMO0lBRUExTCxnREFBU0EsQ0FBQztRQUNSb0csV0FBVztZQUNUN0YscURBQVcsQ0FBQyxjQUFjLE1BQU07Z0JBQUUrRixTQUFTO1lBQUk7WUFDL0MvRix3REFBYyxDQUFDO1FBQ2pCLEdBQUc7UUFFSCxJQUFJc0MsWUFBWSxVQUFVO1lBQ3hCeVAsVUFBVTtRQUNaO1FBQ0EsSUFBSXpQLFlBQVksU0FBU0QsVUFBVTtZQUNqQ1Msb0JBQW9CVCxTQUFTNEQsSUFBSTtRQUNuQztJQUNGLEdBQUc7UUFBQztLQUFFO0lBRU4scUJBQ0U7OzBCQUNFLDhEQUFDK0w7Z0JBQ0NDLFVBQVV6RDtnQkFDVi9FLFdBQVU7O2tDQUVWLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FNYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNwRDs0Q0FBTW9ELFdBQVU7c0RBQWM7Ozs7OztzREFDL0IsOERBQUNoSTs0Q0FDQ3lRLE1BQUs7NENBQ0wzTCxPQUFPMUQsb0JBQW9COzRDQUMzQnNQLFFBQVE7NENBQ1IxSSxXQUFVOzs7Ozs7Ozs7Ozs7OENBR2QsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ3BEOzRDQUFNb0QsV0FBVTtzREFBYzs7Ozs7O3NEQUMvQiw4REFBQ2hJOzRDQUNDeVEsTUFBSzs0Q0FDTDNMLE9BQU94RCxzQkFBcUJWLHFCQUFBQSwrQkFBQUEsU0FBVW1OLEtBQUssS0FBSTs0Q0FDL0MyQyxRQUFROzRDQUNSMUksV0FBVTs7Ozs7Ozs7Ozs7OzhDQUdkLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNwRDs0Q0FBTW9ELFdBQVU7c0RBQWM7Ozs7OztzREFDL0IsOERBQUNoSTs0Q0FDQ3lRLE1BQUs7NENBQ0wzTCxPQUFPLElBQUlFLE9BQU9DLGtCQUFrQixDQUFDOzRDQUNyQ3lMLFFBQVE7NENBQ1IxSSxXQUFVOzs7Ozs7Ozs7Ozs7OENBR2QsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ3BEOzRDQUFNb0QsV0FBVTs7Z0RBQWM7OERBQ3pCLDhEQUFDMkk7b0RBQUszSSxXQUFVOzhEQUF3Qjs7Ozs7Ozs7Ozs7O3NEQUU5Qyw4REFBQzVKLHFEQUFNQTs0Q0FDTDBHLE9BQU85Qzs0Q0FDUDRPLFVBQVUsQ0FBQzFNO2dEQUNUakM7Z0RBQ0E0SixjQUFjM0g7NENBQ2hCOzRDQUNBMk0sU0FBU3JQOzRDQUNUc1AsWUFBWWhRLENBQUFBLGtCQUFBQSw0QkFBQUEsTUFBTzJELE1BQU0sTUFBSyxLQUFLdEI7NENBQ25DNE4sUUFBUXZSOzRDQUNSd1IsYUFBYTs0Q0FDYmhKLFdBQVU7Ozs7Ozt3Q0FFWC9FLE9BQU9qQixJQUFJLGtCQUFJLDhEQUFDaVA7NENBQUVDLE9BQU87Z0RBQUVDLE9BQU87NENBQVU7c0RBQUlsTyxPQUFPakIsSUFBSTs7Ozs7Ozs7Ozs7OzhDQUU5RCw4REFBQytGO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ3BEOzRDQUFNb0QsV0FBVTs7Z0RBQWM7OERBQ2YsOERBQUMySTtvREFBSzNJLFdBQVU7OERBQWU7Ozs7Ozs7Ozs7OztzREFFL0MsOERBQUNoSTs0Q0FDQ3lRLE1BQUs7NENBQ0xXLEtBQUssSUFBSXBNLE9BQU9xTSxXQUFXLEdBQUd6RyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7NENBQzNDOUYsT0FBT2xEOzRDQUNQZ1AsVUFBVSxDQUFDMU07Z0RBQ1RyQyxnQkFBZ0JxQyxFQUFFb04sTUFBTSxDQUFDeE0sS0FBSztnREFDOUJrSCxxQkFBcUI5SCxFQUFFb04sTUFBTSxDQUFDeE0sS0FBSzs0Q0FDckM7NENBQ0FrRCxXQUFVOzRDQUNWMEksVUFBVXZOOzs7Ozs7d0NBRVhSLDZCQUNDLDhEQUFDZ087NENBQUszSSxXQUFVO3NEQUEyQnJGOzs7Ozs7d0NBRTVDTSxPQUFPckIsWUFBWSxrQkFDbEIsOERBQUNxUDs0Q0FBRUMsT0FBTztnREFBRUMsT0FBTzs0Q0FBVTtzREFBSWxPLE9BQU9yQixZQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FrQjVELDhEQUFDbUc7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ3VKO29DQUFHdkosV0FBVTs4Q0FBd0Q7Ozs7Ozs7Ozs7OzBDQUt4RSw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ3BEO3dEQUFNb0QsV0FBVTs7NERBQWM7MEVBQ3JCLDhEQUFDMkk7Z0VBQUszSSxXQUFVOzBFQUFlOzs7Ozs7Ozs7Ozs7a0VBRXpDLDhEQUFDNUoscURBQU1BO3dEQUNMMEcsT0FBTzdELFlBQVlYLE9BQU87d0RBQzFCc1EsVUFBVSxDQUFDWSxNQUFRMUUsd0JBQXdCLFdBQVcwRTt3REFDdERYLFNBQVN6Tzt3REFDVDJPLFFBQVF2Ujt3REFDUndJLFdBQVU7d0RBQ1Z5SixZQUFZO3dEQUNaVCxXQUFXO3dEQUNYRixZQUFZM047Ozs7Ozs7Ozs7OzswREFHaEIsOERBQUM0RTtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNwRDt3REFBTW9ELFdBQVU7OzREQUFjOzBFQUU3Qiw4REFBQzJJO2dFQUNDM0ksV0FBVyxnQkFNVixPQUxDLEdBQUMvRyx1QkFBQUEsWUFBWVgsT0FBTyxjQUFuQlcsMkNBQUFBLHFCQUFxQnVKLGFBQWEsS0FDbkMsR0FBQ3ZKLHdCQUFBQSxZQUFZWCxPQUFPLGNBQW5CVyw2Q0FBQUEsNkJBQUFBLHNCQUFxQnlKLEtBQUssY0FBMUJ6SixpREFBQUEsMkJBQTRCd0QsTUFBTSxLQUNuQ3RCLFlBQ0ksV0FDQTswRUFFUDs7Ozs7Ozs7Ozs7O2tFQUlILDhEQUFDL0UscURBQU1BO3dEQUNMMEcsT0FBTzdELFlBQVlWLElBQUk7d0RBQ3ZCcVEsVUFBVSxDQUFDWSxNQUFRMUUsd0JBQXdCLFFBQVEwRTt3REFDbkRYLFNBQVM1UCxFQUFBQSx3QkFBQUEsWUFBWVgsT0FBTyxjQUFuQlcsNENBQUFBLHNCQUFxQnlKLEtBQUssS0FBSSxFQUFFO3dEQUN6Q3FHLFFBQVF2Ujt3REFDUndJLFdBQVU7d0RBQ1Z5SixZQUFZO3dEQUNaVCxXQUFXO3dEQUNYRixZQUNFLEdBQUM3UCx3QkFBQUEsWUFBWVgsT0FBTyxjQUFuQlcsNENBQUFBLHNCQUFxQnVKLGFBQWEsS0FDbkMsR0FBQ3ZKLHdCQUFBQSxZQUFZWCxPQUFPLGNBQW5CVyw2Q0FBQUEsOEJBQUFBLHNCQUFxQnlKLEtBQUssY0FBMUJ6SixrREFBQUEsNEJBQTRCd0QsTUFBTSxLQUNuQ3RCOzs7Ozs7Ozs7Ozs7MERBSU4sOERBQUM0RTtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNwRDt3REFBTW9ELFdBQVU7OzREQUFjOzBFQUNwQiw4REFBQzJJO2dFQUFLM0ksV0FBVTswRUFBZTs7Ozs7Ozs7Ozs7O2tFQUUxQyw4REFBQ2hJO3dEQUNDeVEsTUFBSzt3REFDTCxVQUFVO3dEQUNWaUIsS0FBSzt3REFDTDVNLE9BQU83RCxZQUFZVCxRQUFRO3dEQUMzQm9RLFVBQVUsQ0FBQzFNLElBQ1Q0SSx3QkFDRSxZQUNBNUksRUFBRW9OLE1BQU0sQ0FBQ3hNLEtBQUssR0FBRyxNQUFNLE1BQU02TSxTQUFTek4sRUFBRW9OLE1BQU0sQ0FBQ3hNLEtBQUs7d0RBR3hEa0QsV0FBVTt3REFDVjBJLFVBQVV2Tjs7Ozs7Ozs7Ozs7OzBEQUdkLDhEQUFDNEU7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDcEQ7d0RBQU1vRCxXQUFVO2tFQUFjOzs7Ozs7a0VBQy9CLDhEQUFDaEk7d0RBQ0N5USxNQUFLO3dEQUNMbUIsV0FBVSxLQUFLLHdCQUF3Qjs7d0RBQ3ZDOU0sT0FBTzdELFlBQVlSLGVBQWU7d0RBQ2xDbVEsVUFBVSxDQUFDMU0sSUFDVDRJLHdCQUF3QixtQkFBbUI1SSxFQUFFb04sTUFBTSxDQUFDeE0sS0FBSzt3REFFM0Q0TCxVQUNFelAsWUFBWVQsUUFBUSxLQUFLLEtBQ3pCMkMsYUFDQSxHQUFDbEMsd0JBQUFBLFlBQVlYLE9BQU8sY0FBbkJXLDRDQUFBQSxzQkFBcUJvSyxjQUFjO3dEQUV0Q3JELFdBQVU7d0RBQ1Y2SixhQUFZOzs7Ozs7Ozs7Ozs7MERBR2hCLDhEQUFDOUo7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDcEQ7d0RBQU1vRCxXQUFVO2tFQUFjOzs7Ozs7a0VBQy9CLDhEQUFDaEk7d0RBQ0M4RSxPQUFPN0QsWUFBWVAsUUFBUTt3REFDM0JrUixXQUFVO3dEQUNWaEIsVUFBVSxDQUFDMU0sSUFDVDRJLHdCQUF3QixZQUFZNUksRUFBRW9OLE1BQU0sQ0FBQ3hNLEtBQUs7d0RBRXBEa0QsV0FBVTt3REFDVjhKLE1BQU07d0RBQ05wQixVQUFVdk47Ozs7Ozs7Ozs7OzswREFHZCw4REFBQzRFO2dEQUFJQyxXQUFVOzBEQUViLDRFQUFDMkk7b0RBQ0N6SSxTQUFTeUU7b0RBQ1QsNEJBQTRCO29EQUM1QjNFLFdBQVcsK0ZBRVYsT0FEQzdFLFlBQVksV0FBVztvREFFekJ1TixVQUFVdk47OERBRVYsNEVBQUMxRSwyRUFBZUE7d0RBQUMwSixNQUFNcEosNEVBQVlBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQU96Qyw4REFBQ2dKO3dDQUNDQyxXQUFVO3dDQUNWa0osT0FBTzs0Q0FBRXZSLFFBQVE7NENBQUtvUyxPQUFPO3dDQUFPO2tEQUVwQyw0RUFBQ2pULHNEQUFXQTs0Q0FDVmtULEtBQUt0UDs0Q0FDTDBGLFlBQVlBOzRDQUNaNkosU0FBU3pQOzRDQUNUMFAsV0FBVzs0Q0FDWGpMLGFBQWFBOzRDQUNiSCxhQUFhQTs7Ozs7Ozs7Ozs7b0NBVWhCekQsY0FBYyxrQ0FDYiw4REFBQzBFOzswREFDQyw4REFBQ2tKOzBEQUFFOzs7Ozs7MERBSUgsOERBQUNsSjtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNDO3dEQUNDQyxTQUFTakU7d0RBQ1QrRCxXQUFVO2tFQUNYOzs7Ozs7a0VBR0QsOERBQUNDO3dEQUNDQyxTQUFTOzREQUNQeUUsbUJBQW1CLHNCQUFzQjs0REFDekMxSTt3REFDRjt3REFDQStELFdBQVU7a0VBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FTWCw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDQztnQ0FDQ3dJLE1BQUs7Z0NBQ0x2SSxTQUFTO29DQUNQNUYsT0FBT29ILElBQUksQ0FBQztnQ0FDZDtnQ0FDQTFCLFdBQVU7Z0NBQ1YwSSxVQUFVeE87MENBQ1g7Ozs7OzswQ0FHRCw4REFBQytGO2dDQUNDd0ksTUFBSztnQ0FDTHZJLFNBQVM7b0NBQ1AsSUFBSXJILGFBQWEsUUFBUTt3Q0FDdkI2RSxRQUFRQyxHQUFHLENBQUMscUJBQXFCNUI7d0NBQ2pDLElBQUlBLG1CQUFtQjs0Q0FDckJULGFBQWE7NENBQ2JuQyxVQUFVO3dDQUNaLE9BQU87NENBQ0w0TyxhQUFhO3dDQUNmO29DQUNGLE9BQU87d0NBQ0xoRCxhQUFhO29DQUNmO2dDQUNGO2dDQUNBMkQsVUFBVXhPLFdBQVdXLGtCQUFrQjtnQ0FDdkNtRixXQUFXLGdFQUVWLE9BRENuRixrQkFBa0IsSUFBSSxXQUFXOzBDQUVwQzs7Ozs7OzBDQUdELDhEQUFDb0Y7Z0NBQ0N3SSxNQUFLO2dDQUNMdkksU0FBUztvQ0FDUCxJQUFJbkUsbUJBQW1CO3dDQUNyQlQsYUFBYTt3Q0FDYm5DLFVBQVU7b0NBQ1osT0FBTzt3Q0FDTCxJQUFJLENBQUNtSyxZQUFZO3dDQUNqQmhJLGFBQWE7d0NBQ2JuQyxVQUFVO29DQUNaO2dDQUNGO2dDQUNBNkcsV0FBVyxxRkFFVixPQURDbkYsa0JBQWtCLElBQUksV0FBVztnQ0FFbkM2TixVQUFVeE8sV0FBV1csa0JBQWtCOzBDQUN4Qzs7Ozs7OzBDQUdELDhEQUFDb0Y7Z0NBQ0N3SSxNQUFLO2dDQUNMdkksU0FBUztvQ0FDUCxJQUFJLENBQUNvRCxZQUFZO29DQUNqQmhJLGFBQWE7b0NBQ2JuQyxVQUFVO2dDQUNaO2dDQUNBNkcsV0FBVyxxREFHVHBILE9BRkEsQ0FBQ21DLGtCQUFrQm9QLFFBQVEsQ0FBQyxLQUFLLFdBQVcsSUFDN0MsTUFNQSxPQUxDdlIsU0FBU3dSLE9BQU8sS0FBSyxLQUNyQnhSLFNBQVN3UixPQUFPLEtBQUssS0FDckJ4UixTQUFTd1IsT0FBTyxLQUFLLElBQ2pCLEtBQ0E7MENBRVA7Ozs7OzswQ0FHRCw4REFBQ25LO2dDQUNDd0ksTUFBSztnQ0FDTHZJLFNBQVM7b0NBQ1AsSUFBSSxDQUFDb0QsWUFBWTtvQ0FDakJoSSxhQUFhO29DQUNibkMsVUFBVTtnQ0FDWjtnQ0FDQTZHLFdBQVcsdURBR1RwSCxPQUZBLENBQUNtQyxrQkFBa0JvUCxRQUFRLENBQUMsS0FBSyxXQUFXLElBQzdDLE1BTUEsT0FMQ3ZSLFNBQVN3UixPQUFPLEtBQUssS0FDckJ4UixTQUFTd1IsT0FBTyxLQUFLLEtBQ3JCeFIsU0FBU3dSLE9BQU8sS0FBSyxJQUNqQixLQUNBOzBDQUVQOzs7Ozs7MENBR0QsOERBQUNuSztnQ0FDQ3dJLE1BQUs7Z0NBQ0x2SSxTQUFTO29DQUNQLDhCQUE4QjtvQ0FDOUIsdUJBQXVCO29DQUN2QixhQUFhO29DQUNiMEcsd0JBQXdCLEdBQUc7Z0NBQzNCLE1BQU07Z0NBQ1I7Z0NBQ0E1RyxXQUFXLHVEQUdUcEgsT0FGQW1DLGtCQUFrQm9QLFFBQVEsQ0FBQyxLQUFLLEtBQUssVUFDdEMsTUFNQSxPQUxDdlIsU0FBU3dSLE9BQU8sS0FBSyxLQUNyQnhSLFNBQVN3UixPQUFPLEtBQUssS0FDckJ4UixTQUFTd1IsT0FBTyxLQUFLLElBQ2pCLEtBQ0E7MENBSVA7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFLTCw4REFBQ2xULGtHQUFVQTtnQkFBQ21ULE1BQU07Z0JBQUNDLE1BQU1wUjtnQkFBUXFSLElBQUlwVSwyQ0FBUUE7MEJBQzNDLDRFQUFDYyw4RkFBTUE7b0JBQ0xzVCxJQUFHO29CQUNIdkssV0FBVTtvQkFDVndLLFNBQVMsQ0FBQzFOO3dCQUNSLElBQUl6QixjQUFjLFlBQVlJLHFCQUFxQjs0QkFDakRRO3dCQUNGO29CQUNGOztzQ0FFQSw4REFBQy9FLGtHQUFVQSxDQUFDdVQsS0FBSzs0QkFDZkYsSUFBSXBVLDJDQUFRQTs0QkFDWnVVLE9BQU07NEJBQ05DLFdBQVU7NEJBQ1ZDLFNBQVE7NEJBQ1JDLE9BQU07NEJBQ05DLFdBQVU7NEJBQ1ZDLFNBQVE7c0NBRVIsNEVBQUNoTDtnQ0FBSUMsV0FBVTs7Ozs7Ozs7Ozs7c0NBR2pCLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUM5SSxrR0FBVUEsQ0FBQ3VULEtBQUs7b0NBQ2ZGLElBQUlwVSwyQ0FBUUE7b0NBQ1p1VSxPQUFNO29DQUNOQyxXQUFVO29DQUNWQyxTQUFRO29DQUNSQyxPQUFNO29DQUNOQyxXQUFVO29DQUNWQyxTQUFROzhDQUVSLDRFQUFDOVQsOEZBQU1BLENBQUMrVCxLQUFLO3dDQUFDaEwsV0FBVTtrREFFdEIsNEVBQUNEOzRDQUFJQyxXQUFVOzs4REFFYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDdUo7NERBQUd2SixXQUFVOzs4RUFDWiw4REFBQzJJO29FQUFLM0ksV0FBVTs7c0ZBQ2QsOERBQUN2SiwyRUFBZUE7NEVBQUMwSixNQUFNeEosc0VBQU1BOzs7Ozs7d0VBQUs7Ozs7Ozs7Z0VBQzVCO2dFQUNOLFdBY0QsT0FiQzBFLGFBQWEsV0FDVCxlQUNBQSxhQUFhLFdBQ2IsY0FDQUEsYUFBYSxZQUNiLGFBQ0FBLGFBQWEsZ0JBQ2IsZ0JBQ0FBLGFBQWEsU0FDYixTQUNBQSxhQUFhLHVCQUNiLGVBQ0E7Ozs7Ozs7c0VBR1IsOERBQUM0RTs0REFDQ0MsU0FBU2pFOzREQUNUd00sTUFBSzs0REFDTHpJLFdBQVU7NERBQ1ZpTCxtQkFBZ0I7OzhFQUVoQiw4REFBQ3hVLDJFQUFlQTtvRUFDZDBKLE1BQU12Six1RUFBT0E7b0VBQ2JvSixXQUFVOzs7Ozs7Z0VBQ1Q7Ozs7Ozs7Ozs7Ozs7OERBSVAsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ2lKOzREQUFFakosV0FBVTs7Z0VBQXdEO2dFQUMxQztnRUFDeEIzRSxhQUFhLFdBQ1YsV0FDQUEsYUFBYSxXQUNiLFdBQ0FBLGFBQWEsWUFDYixZQUNBQSxhQUFhLGdCQUNiLG1DQUNBQSxhQUFhLFNBQ2IsdUVBQ0FBLGFBQWEsdUJBQ2IsMkVBQ0E7Z0VBQUs7Z0VBQUk7Z0VBQ0M7Ozs7Ozs7d0RBQ1g7d0RBQ0pBLGFBQWEsMEJBQ1osOERBQUMwRTs7OEVBRUMsOERBQUNtTDtvRUFDQ2xMLFdBQVU7b0VBQ1Y4SixNQUFLO29FQUNMaE4sT0FBT3ZCO29FQUNQcU4sVUFBVSxDQUFDMU07d0VBQ1RWLHNCQUFzQlUsRUFBRW9OLE1BQU0sQ0FBQ3hNLEtBQUs7d0VBQ3BDLElBQUlaLEVBQUVvTixNQUFNLENBQUN4TSxLQUFLLEVBQUU7NEVBQ2xCcEIsdUJBQXVCO3dFQUN6QjtvRUFDRjtvRUFDQXlQLFFBQVEsQ0FBQ2pQO3dFQUNQLE1BQU1rUCxlQUFlN0csY0FDbkJySSxFQUFFb04sTUFBTSxDQUFDeE0sS0FBSzt3RUFFaEJ0QixzQkFBc0I0UDtvRUFDeEI7b0VBQ0F2QixhQUFZO29FQUNaRCxXQUFVOzs7Ozs7Z0VBRVh2TyxhQUFhLFlBQVksQ0FBQ0kscUNBQ3pCLDhEQUFDa047b0VBQUszSSxXQUFVOzhFQUFlOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBUXZDLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNDOzREQUNDQyxTQUFTO2dFQUNQakUsY0FBY1Qsc0JBQXNCOzREQUN0Qzs0REFDQXlQLG1CQUFnQjs0REFDaEJ4QyxNQUFLOzREQUNMekksV0FBVTtzRUFDWDs7Ozs7O3NFQUdELDhEQUFDQzs0REFDQ0MsU0FBUztnRUFDUCxJQUNFN0UsY0FBYyxZQUNkLEVBQUNFLCtCQUFBQSx5Q0FBQUEsbUJBQW9CaUosSUFBSSxLQUN6QjtvRUFDQTlJLHVCQUF1QjtvRUFDdkI7Z0VBQ0Y7Z0VBQ0EsSUFBSTdDLFlBQVksT0FBTztvRUFDckIsSUFBSXdDLGFBQWEsVUFBVTt3RUFDekIwSixhQUFhO29FQUNmLE9BQU8sSUFBSTFKLGNBQWMsc0JBQXNCO3dFQUM3QzBKLGFBQWE7b0VBQ2Y7Z0VBQ0YsT0FBTztvRUFDTCxJQUFJMUosYUFBYSxVQUFVO3dFQUN6QjBNLGFBQWE7b0VBQ2YsT0FBTyxJQUFJMU0sY0FBYyxVQUFVO3dFQUNqQ3VMLHdCQUF3QixHQUFHO29FQUM3QixPQUFPLElBQUl2TCxjQUFjLFdBQVc7d0VBQ2xDdUwsd0JBQXdCLEdBQUc7b0VBQzdCLE9BQU8sSUFBSXZMLGNBQWMsUUFBUTt3RUFDL0IwTSxhQUFhO29FQUNmLE9BQU8sSUFBSTFNLGNBQWMsZUFBZTt3RUFDdEN1TCx3QkFBd0IsR0FBRztvRUFDN0IsT0FBTyxJQUFJdkwsY0FBYyxzQkFBc0I7d0VBQzdDME0sYUFBYTtvRUFDZjtnRUFDRjtnRUFDQXZNLHNCQUFzQjtnRUFDdEJTOzREQUNGOzREQUNBZ1AsbUJBQWdCOzREQUNoQnhDLE1BQUs7NERBQ0x6SSxXQUFVO3NFQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFhdkI7R0E3OUNNckg7O1FBa0JXckMsa0RBQVNBOzs7S0FsQnBCcUM7QUErOUNOLCtEQUFlQSxhQUFhQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2NvbXBvbmVudHMvUHBlQ29uc3VtYWJsZS5qcz9lYTY3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyB1c2VFZmZlY3QsIHVzZVJlZiwgdXNlU3RhdGUsIEZyYWdtZW50IH0gZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCBTZWxlY3QgZnJvbSBcInJlYWN0LXNlbGVjdFwiO1xyXG5pbXBvcnQgeyB0b2FzdCB9IGZyb20gXCJyZWFjdC10b2FzdGlmeVwiO1xyXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tIFwibmV4dC9yb3V0ZXJcIjtcclxuaW1wb3J0IENvb2tpZXMgZnJvbSBcImpzLWNvb2tpZVwiO1xyXG5pbXBvcnQgeyBhcGlDb25maWcgfSBmcm9tIFwiQC9zZXJ2aWNlcy9hcGlDb25maWdcIjtcclxuaW1wb3J0IHsgRm9udEF3ZXNvbWVJY29uIH0gZnJvbSBcIkBmb3J0YXdlc29tZS9yZWFjdC1mb250YXdlc29tZVwiO1xyXG5pbXBvcnQge1xyXG4gIGZhUGVuVG9TcXVhcmUsXHJcbiAgZmFJbmZvLFxyXG4gIGZhWG1hcmssXHJcbiAgLy8gZmFDaXJjbGVDaGVjayxcclxuICBmYVNxdWFyZUNoZWNrLFxyXG59IGZyb20gXCJAZm9ydGF3ZXNvbWUvZnJlZS1zb2xpZC1zdmctaWNvbnNcIjtcclxuaW1wb3J0IHsgQWdHcmlkUmVhY3QgfSBmcm9tIFwiYWctZ3JpZC1yZWFjdFwiO1xyXG5pbXBvcnQgXCJhZy1ncmlkLWNvbW11bml0eS9zdHlsZXMvL2FnLWdyaWQuY3NzXCI7XHJcbmltcG9ydCBcImFnLWdyaWQtY29tbXVuaXR5L3N0eWxlcy8vYWctdGhlbWUtYWxwaW5lLmNzc1wiO1xyXG5pbXBvcnQgeyBmYUZsb3BweURpc2ssIGZhVHJhc2ggfSBmcm9tIFwiQGZvcnRhd2Vzb21lL2ZyZWUtc29saWQtc3ZnLWljb25zXCI7XHJcbmltcG9ydCB7IERpYWxvZywgVHJhbnNpdGlvbiB9IGZyb20gXCJAaGVhZGxlc3N1aS9yZWFjdFwiO1xyXG5pbXBvcnQgeyBsb2dvdXQgfSBmcm9tIFwiQC91dGlscy9zZWN1cmVTdG9yYWdlXCI7XHJcbmltcG9ydCB7XHJcbiAgZmFYbWFya0NpcmNsZSxcclxuICBmYUNpcmNsZUNoZWNrLFxyXG59IGZyb20gXCJAZm9ydGF3ZXNvbWUvZnJlZS1yZWd1bGFyLXN2Zy1pY29uc1wiO1xyXG5pbXBvcnQgcHJvZHVjdFN0YXR1c1JlbmRlcmVyIGZyb20gXCJAL3V0aWxzL3JlbmRlcmVyL3Byb2R1Y3RTdGF0dXNSZW5kZXJlclwiO1xyXG5pbXBvcnQgeyBmb3JFYWNoIH0gZnJvbSBcImxvZGFzaFwiO1xyXG5cclxuY29uc3QgY3VzdG9tU2VsZWN0U3R5bGVzID0ge1xyXG4gIGNvbnRyb2w6IChiYXNlKSA9PiAoe1xyXG4gICAgLi4uYmFzZSxcclxuICAgIGhlaWdodDogXCIyOHB4XCIsXHJcbiAgICBtaW5IZWlnaHQ6IFwiMjhweFwiLFxyXG4gIH0pLFxyXG4gIHZhbHVlQ29udGFpbmVyOiAocHJvdmlkZWQpID0+ICh7XHJcbiAgICAuLi5wcm92aWRlZCxcclxuICAgIGhlaWdodDogXCIyOHB4XCIsXHJcbiAgICBwYWRkaW5nOiBcIjAgNnB4XCIsXHJcbiAgfSksXHJcbiAgaW5wdXQ6IChwcm92aWRlZCkgPT4gKHtcclxuICAgIC4uLnByb3ZpZGVkLFxyXG4gICAgbWFyZ2luOiBcIjBweFwiLFxyXG4gIH0pLFxyXG4gIGluZGljYXRvclNlcGFyYXRvcjogKCkgPT4gKHtcclxuICAgIGRpc3BsYXk6IFwibm9uZVwiLFxyXG4gIH0pLFxyXG4gIGluZGljYXRvcnNDb250YWluZXI6IChwcm92aWRlZCkgPT4gKHtcclxuICAgIC4uLnByb3ZpZGVkLFxyXG4gICAgaGVpZ2h0OiBcIjI4cHhcIixcclxuICB9KSxcclxufTtcclxuXHJcbmNvbnN0IGVtcHR5SXRlbSA9IHtcclxuICBwcm9kdWN0OiBudWxsLFxyXG4gIHNpemU6IG51bGwsXHJcbiAgcXVhbnRpdHk6IFwiXCIsXHJcbiAgbmFtZUZvclByaW50aW5nOiBcIlwiLFxyXG4gIGNvbW1lbnRzOiBcIlwiLFxyXG59O1xyXG5jb25zdCBQcGVDb25zdW1hYmxlID0gKHtcclxuICB1c2VyRGF0YSxcclxuICBwYWdlVHlwZSxcclxuICBzaXRlcyxcclxuICBPcmRlclJlcXVlc3REYXRhLFxyXG4gIHBwZUlkID0gbnVsbCxcclxufSkgPT4ge1xyXG4gIGNvbnN0IFtpc09wZW4sIHNldElzT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW25hbWVPZk9yaWdpbmF0b3IsIHNldE5hbWVPZk9yaWdpbmF0b3JdID0gdXNlU3RhdGUoXCJcIik7XHJcbiAgY29uc3QgW2VtYWlsT2ZPcmlnaW5hdG9yLCBzZXRFbWFpbE9mT3JpZ2luYXRvcl0gPSB1c2VTdGF0ZShcIlwiKTtcclxuICBjb25zdCBbc2l0ZURhdGEsIHNldFNpdGVEYXRhXSA9IHVzZVN0YXRlKHt9KTtcclxuXHJcbiAgY29uc3QgW2l0ZW1zLCBzZXRJdGVtc10gPSB1c2VTdGF0ZShbeyAuLi5lbXB0eUl0ZW0gfV0pO1xyXG4gIGNvbnN0IFtyZXF1aXJlZERhdGUsIHNldFJlcXVpcmVkRGF0ZV0gPSB1c2VTdGF0ZShcIlwiKTtcclxuICBjb25zdCBbY3JlYXRlZERhdGUsIHNldENyZWF0ZWREYXRlXSA9IHVzZVN0YXRlKFwiXCIpO1xyXG4gIGNvbnN0IFtzaXRlLCBzZXRTaXRlXSA9IHVzZVN0YXRlKHNpdGVzPy5bMF0gfHwgbnVsbCk7XHJcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtwcm9kdWN0c0RhdGEsIHNldFByb2R1Y3RzRGF0YV0gPSB1c2VTdGF0ZSgpO1xyXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xyXG4gIGNvbnN0IFtjdXJyZW50SXRlbSwgc2V0Q3VycmVudEl0ZW1dID0gdXNlU3RhdGUoeyAuLi5lbXB0eUl0ZW0gfSk7XHJcbiAgY29uc3QgW3NhdmVkSXRlbXMsIHNldFNhdmVkSXRlbXNdID0gdXNlU3RhdGUoW10pO1xyXG4gIGNvbnN0IGdyaWRSZWYgPSB1c2VSZWYobnVsbCk7XHJcbiAgLy8gY29uc29sZS5sb2coXCJkc2tzbGRpYmlhc2RiaWxzZGJ2XCIsIHNpdGVEYXRhKTtcclxuXHJcbiAgLy8gQWRkIHZhbGlkYXRpb24gc3RhdGVzIGZvciBjdXJyZW50IGl0ZW1cclxuICAvLyBjb25zdCBbcHJvZHVjdFZhbGlkLCBzZXRQcm9kdWN0VmFsaWRdID0gdXNlU3RhdGUoXCJcIik7XHJcbiAgLy8gY29uc3QgW3F1YW50aXR5VmFsaWQsIHNldFF1YW50aXR5VmFsaWRdID0gdXNlU3RhdGUoXCJcIik7XHJcblxyXG4gIGNvbnN0IFtkYXRlV2FybmluZywgc2V0RGF0ZVdhcm5pbmddID0gdXNlU3RhdGUoXCJcIik7XHJcblxyXG4gIC8vIGNvbnN0IFtpc0VkaXRNb2RlLCBzZXRJc0VkaXRNb2RlXSA9IHVzZVN0YXRlKCk7XHJcbiAgY29uc3QgW3JlcXVlc3RTdGF0dXMsIHNldFJlcXVlc3RTdGF0dXNdID0gdXNlU3RhdGUoNSk7XHJcbiAgY29uc3QgW3JlcXVlc3RTdGF0dXNMaXN0LCBzZXRSZXF1ZXN0U3RhdHVzTGlzdF0gPSB1c2VTdGF0ZShcIlwiKTtcclxuICBjb25zdCBbZXJyb3JzLCBzZXRFcnJvcnNdID0gdXNlU3RhdGUoe30pO1xyXG4gIGNvbnN0IFtzdWJtaXR0ZWQsIHNldFN1Ym1pdHRlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3BvcHVwVHlwZSwgc2V0UG9wdXBUeXBlXSA9IHVzZVN0YXRlKFwiXCIpO1xyXG4gIGNvbnN0IFtjYW5jZWxsZWRSZWFzb25hcGksIHNldENhbmNlbGxlZFJlYXNvbmFwaV0gPSB1c2VTdGF0ZShcIlwiKTtcclxuICBjb25zdCBbaXNWYWxpZENhbmNlbFJlYXNvbiwgc2V0SXNWYWxpZENhbmNlbFJlYXNvbl0gPSB1c2VTdGF0ZSh0cnVlKTtcclxuICBjb25zdCBbZWRpdGluZ1Jvd0lkLCBzZXRFZGl0aW5nUm93SWRdID0gdXNlU3RhdGUobnVsbCk7XHJcbiAgY29uc3QgW2dyaWRBcGksIHNldEdyaWRBcGldID0gdXNlU3RhdGUobnVsbCk7XHJcblxyXG4gIGNvbnN0IFtoYXNVbnNhdmVkQ2hhbmdlcywgc2V0SGFzVW5zYXZlZENoYW5nZXNdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG5cclxuICBjb25zdCBjbG9zZU1vZGFsID0gKGUpID0+IHtcclxuICAgIGlmIChlKSB7XHJcbiAgICAgIGUucHJldmVudERlZmF1bHQoKTtcclxuICAgIH1cclxuICAgIHNldElzVmFsaWRDYW5jZWxSZWFzb24odHJ1ZSk7XHJcbiAgICBzZXRDYW5jZWxsZWRSZWFzb25hcGkoXCJcIik7XHJcbiAgICBzZXRJc09wZW4oZmFsc2UpO1xyXG4gIH07XHJcbiAgLy8jcmVnaW9uIExvYWQgZGF0YVxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHtcclxuICAgICAgQ29va2llcy5zZXQoXCJyYXdXYXJuaW5nXCIsIHRydWUsIHsgZXhwaXJlczogMzY1IH0pO1xyXG4gICAgICBDb29raWVzLnJlbW92ZShcImZpbmlzaFdhcm5pbmdcIik7XHJcbiAgICB9LCAyMDAwKTtcclxuXHJcbiAgICBpZiAocGFnZVR5cGUgPT0gXCJhZGRcIiAmJiB1c2VyRGF0YSkge1xyXG4gICAgICBzZXROYW1lT2ZPcmlnaW5hdG9yKHVzZXJEYXRhLm5hbWUpO1xyXG4gICAgfVxyXG4gICAgaWYgKE9yZGVyUmVxdWVzdERhdGEgfHwgT3JkZXJSZXF1ZXN0RGF0YT8ubGVuZ3RoID4gMCkge1xyXG4gICAgICAvLyBjb25zb2xlLmxvZyhcIk9yZGVyUmVxdWVzdERhdGFcIiwgT3JkZXJSZXF1ZXN0RGF0YSk7XHJcbiAgICAgIGlmIChPcmRlclJlcXVlc3REYXRhWzBdPy5hY3Rpb25faWQpIHtcclxuICAgICAgICBzZXRSZXF1ZXN0U3RhdHVzKE9yZGVyUmVxdWVzdERhdGFbMF0/LmFjdGlvbl9pZCk7XHJcbiAgICAgICAgaWYgKE9yZGVyUmVxdWVzdERhdGFbMF0/LmFjdGlvbl9pZCAhPT0gNSkge1xyXG4gICAgICAgICAgc2V0U3VibWl0dGVkKHRydWUpO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgICBpZiAoT3JkZXJSZXF1ZXN0RGF0YVswXT8uc2l0ZV9pZCkge1xyXG4gICAgICAgIHNldFNpdGUoe1xyXG4gICAgICAgICAgbGFiZWw6IE9yZGVyUmVxdWVzdERhdGFbMF0/LnNpdGVfbmFtZSxcclxuICAgICAgICAgIHZhbHVlOiBPcmRlclJlcXVlc3REYXRhWzBdPy5zaXRlX2lkLFxyXG4gICAgICAgIH0pO1xyXG4gICAgICB9XHJcbiAgICAgIGlmIChPcmRlclJlcXVlc3REYXRhWzBdPy5yZXF1aXJlZF9kYXRlKSB7XHJcbiAgICAgICAgc2V0UmVxdWlyZWREYXRlKFxyXG4gICAgICAgICAgT3JkZXJSZXF1ZXN0RGF0YVswXT8ucmVxdWlyZWRfZGF0ZVxyXG4gICAgICAgICAgICA/IG5ldyBEYXRlKE9yZGVyUmVxdWVzdERhdGFbMF0/LnJlcXVpcmVkX2RhdGUpLnRvTG9jYWxlRGF0ZVN0cmluZyhcclxuICAgICAgICAgICAgICAgIFwiZW4tQ0FcIixcclxuICAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgeWVhcjogXCJudW1lcmljXCIsXHJcbiAgICAgICAgICAgICAgICAgIG1vbnRoOiBcIjItZGlnaXRcIixcclxuICAgICAgICAgICAgICAgICAgZGF5OiBcIjItZGlnaXRcIixcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgIDogXCJcIlxyXG4gICAgICAgICk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGlmIChPcmRlclJlcXVlc3REYXRhWzBdPy5jcmVhdGVkX2F0KSB7XHJcbiAgICAgICAgc2V0Q3JlYXRlZERhdGUoXHJcbiAgICAgICAgICBPcmRlclJlcXVlc3REYXRhWzBdPy5jcmVhdGVkX2F0XHJcbiAgICAgICAgICAgID8gbmV3IERhdGUoT3JkZXJSZXF1ZXN0RGF0YVswXT8uY3JlYXRlZF9hdCkudG9Mb2NhbGVEYXRlU3RyaW5nKFxyXG4gICAgICAgICAgICAgICAgXCJlbi1DQVwiLFxyXG4gICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICB5ZWFyOiBcIm51bWVyaWNcIixcclxuICAgICAgICAgICAgICAgICAgbW9udGg6IFwiMi1kaWdpdFwiLFxyXG4gICAgICAgICAgICAgICAgICBkYXk6IFwiMi1kaWdpdFwiLFxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgOiBcIlwiXHJcbiAgICAgICAgKTtcclxuICAgICAgfVxyXG4gICAgICBpZiAoT3JkZXJSZXF1ZXN0RGF0YVswXS51c2VyX25hbWUpIHtcclxuICAgICAgICAvLyBzZXROYW1lT2ZPcmlnaW5hdG9yKCk7XHJcbiAgICAgICAgc2V0TmFtZU9mT3JpZ2luYXRvcihPcmRlclJlcXVlc3REYXRhWzBdLnVzZXJfbmFtZSk7XHJcbiAgICAgIH1cclxuICAgICAgaWYgKE9yZGVyUmVxdWVzdERhdGFbMF0uYWN0aW9uZWRfYnlfZW1haWwpIHtcclxuICAgICAgICAvLyBzZXROYW1lT2ZPcmlnaW5hdG9yKCk7XHJcbiAgICAgICAgc2V0RW1haWxPZk9yaWdpbmF0b3IoT3JkZXJSZXF1ZXN0RGF0YVswXS5hY3Rpb25lZF9ieV9lbWFpbCk7XHJcbiAgICAgIH1cclxuICAgICAgLy8gc2V0cm93ZGF0YVxyXG4gICAgICBpZiAoT3JkZXJSZXF1ZXN0RGF0YSAmJiBBcnJheS5pc0FycmF5KE9yZGVyUmVxdWVzdERhdGEpKSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coXCJsb2FkIGRhdGFcIiwgT3JkZXJSZXF1ZXN0RGF0YSwgcmVxdWVzdFN0YXR1cyk7XHJcbiAgICAgICAgY29uc3QgU1RBVFVTX01BUCA9IHtcclxuICAgICAgICAgIDE6IFwiUGVuZGluZyBSZXZpZXdcIixcclxuICAgICAgICAgIDI6IFwiQXBwcm92ZWRcIixcclxuICAgICAgICAgIDM6IFwiUmVqZWN0ZWRcIixcclxuICAgICAgICAgIDQ6IFwiT3JkZXJlZFwiLFxyXG4gICAgICAgICAgNTogXCJEcmFmdFwiLFxyXG4gICAgICAgIH07XHJcbiAgICAgICAgY29uc3QgbWFwcGVkSXRlbXMgPSBPcmRlclJlcXVlc3REYXRhLm1hcCgoaXRlbSkgPT4gKHtcclxuICAgICAgICAgIGlkOiBpdGVtLm9yZGVyX3JlcXVlc3RfaXRlbXMsXHJcbiAgICAgICAgICBwcm9kdWN0OiB7IHZhbHVlOiBpdGVtLnByb2R1Y3RfaWQsIGxhYmVsOiBpdGVtLnByb2R1Y3RfbmFtZSB9LFxyXG4gICAgICAgICAgc2l6ZTogeyB2YWx1ZTogaXRlbS5zaXplLCBsYWJlbDogaXRlbS5zaXplIHx8IFwiTi9BXCIgfSxcclxuICAgICAgICAgIHF1YW50aXR5OiBpdGVtLnF1YW50aXR5LFxyXG4gICAgICAgICAgbmFtZUZvclByaW50aW5nOiBpdGVtLm5hbWVfZm9yX3ByaW50aW5nLFxyXG4gICAgICAgICAgY29tbWVudHM6IGl0ZW0uY29tbWVudHMgfHwgXCJcIixcclxuICAgICAgICAgIHVwZGF0ZUZsYWc6IDAsXHJcbiAgICAgICAgICBzdGF0dXM6IFNUQVRVU19NQVBbaXRlbS5hY3Rpb25faWRdLFxyXG4gICAgICAgIH0pKTtcclxuICAgICAgICBjb25zdCB1bmlxdWVWYWx1ZXMgPSBbXHJcbiAgICAgICAgICAuLi5uZXcgU2V0KE9yZGVyUmVxdWVzdERhdGEubWFwKChpdGVtKSA9PiBpdGVtLmFjdGlvbl9pZCkpLFxyXG4gICAgICAgIF07XHJcbiAgICAgICAgc2V0UmVxdWVzdFN0YXR1c0xpc3QodW5pcXVlVmFsdWVzKTtcclxuICAgICAgICBzZXRTYXZlZEl0ZW1zKG1hcHBlZEl0ZW1zKTtcclxuICAgICAgICBjb25zb2xlLmxvZyhcInVuaXF1ZVZhbHVlc1wiLCB1bmlxdWVWYWx1ZXMpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfSwgW09yZGVyUmVxdWVzdERhdGFdKTtcclxuICAvLyNlbmRyZWdpb24gTG9hZCBkYXRhXHJcblxyXG4gIC8vI3JlZ2lvbiBhZy1ncmlkXHJcblxyXG4gIGNvbnN0IG9uUm93U2VsZWN0ZWQgPSAoZXZlbnQpID0+IHtcclxuICAgIGlmIChldmVudC5ub2RlLmlzU2VsZWN0ZWQoKSkge1xyXG4gICAgICBzZXRFZGl0aW5nUm93SWQoZXZlbnQuZGF0YS5pZCk7XHJcbiAgICAgIHNldEN1cnJlbnRJdGVtKGV2ZW50LmRhdGEpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IG9uR3JpZFJlYWR5ID0gKHBhcmFtcykgPT4ge1xyXG4gICAgc2V0R3JpZEFwaShwYXJhbXMuYXBpKTtcclxuICB9O1xyXG5cclxuICAvLyBpbnNpZGUgeW91ciBjb21wb25lbnRcclxuICBjb25zdCBnZXRSb3dDbGFzcyA9IChwYXJhbXMpID0+IHtcclxuICAgIHJldHVybiBwYXJhbXMuZGF0YS5pZCA9PT0gZWRpdGluZ1Jvd0lkID8gXCJiZy1ibHVlLTEwMFwiIDogXCJcIjtcclxuICB9O1xyXG5cclxuICBjb25zdCBJY29uc1JlbmRlcmVyID0gKHByb3BzKSA9PiB7XHJcbiAgICBjb25zdCBoYW5kbGVEZWxldGUgPSAoZXZlbnQpID0+IHtcclxuICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcclxuICAgICAgY29uc3Qgc2VsZWN0ZWRSb3cgPSBwcm9wcy5kYXRhO1xyXG4gICAgICBjb25zdCB1cGRhdGVkRGF0YSA9IHNhdmVkSXRlbXMuZmlsdGVyKChpdGVtKSA9PiBpdGVtICE9PSBzZWxlY3RlZFJvdyk7XHJcbiAgICAgIHNldFNhdmVkSXRlbXModXBkYXRlZERhdGEpO1xyXG4gICAgfTtcclxuXHJcbiAgICBjb25zdCBoYW5kbGVFZGl0ID0gKGV2ZW50KSA9PiB7XHJcbiAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XHJcbiAgICAgIGNvbnN0IHNlbGVjdGVkUm93ID0gcHJvcHMuZGF0YTtcclxuICAgICAgc2V0SGFzVW5zYXZlZENoYW5nZXModHJ1ZSk7XHJcbiAgICAgIHNldEVkaXRpbmdSb3dJZChzZWxlY3RlZFJvdy5pZCk7XHJcbiAgICAgIC8vIHNldEN1cnJlbnRJdGVtKEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkoc2VsZWN0ZWRSb3cpKSk7XHJcblxyXG4gICAgICBjb25zdCB1cGRhdGVkRGF0YSA9IHNhdmVkSXRlbXMuZmlsdGVyKChpdGVtKSA9PiBpdGVtICE9PSBzZWxlY3RlZFJvdyk7XHJcbiAgICAgIHNldFNhdmVkSXRlbXModXBkYXRlZERhdGEpO1xyXG5cclxuICAgICAgY29uc3QgaGFuZGxlUm93VXBkYXRlID0gKGFjdGlvbklkKSA9PiAoKSA9PiB7XHJcbiAgICAgICAgc2V0U2VsZWN0ZWRSb3dEYXRhKHBhcmFtcy5kYXRhKTtcclxuICAgICAgICBzZXRTdGF0dXNBY3Rpb24oYWN0aW9uSWQpO1xyXG4gICAgICAgIHNldFNob3dTdGF0dXNEaWFsb2codHJ1ZSk7XHJcbiAgICAgIH07XHJcblxyXG4gICAgICAvLyBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xyXG4gICAgICAvLyBjb25zdCBzZWxlY3RlZFJvdyA9IHByb3BzLmRhdGE7XHJcbiAgICAgIC8vIHNldEVkaXRpbmdSb3dJZChzZWxlY3RlZFJvdy5pZCB8fCBwcm9wcy5ub2RlLmlkKTtcclxuICAgICAgLy8gLy8gY29uc3QgdXBkYXRlZERhdGEgPSBzYXZlZEl0ZW1zLmZpbHRlcigoaXRlbSkgPT4gaXRlbSAhPT0gc2VsZWN0ZWRSb3cpO1xyXG4gICAgICAvLyAvLyBzZXRTYXZlZEl0ZW1zKHVwZGF0ZWREYXRhKTtcclxuICAgICAgLy8gICBzZXRDdXJyZW50SXRlbShKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHNlbGVjdGVkUm93KSkpO1xyXG5cclxuICAgICAgLy8gUG9wdWxhdGUgdGhlIGZvcm0gd2l0aCB0aGUgc2VsZWN0ZWQgcm93IGRhdGFcclxuICAgICAgc2V0Q3VycmVudEl0ZW0oe1xyXG4gICAgICAgIGlkOiBzZWxlY3RlZFJvdy5pZCxcclxuICAgICAgICBwcm9kdWN0OiBzZWxlY3RlZFJvdy5wcm9kdWN0LFxyXG4gICAgICAgIHNpemU6IHNlbGVjdGVkUm93LnNpemUsXHJcbiAgICAgICAgcXVhbnRpdHk6IHNlbGVjdGVkUm93LnF1YW50aXR5LFxyXG4gICAgICAgIG5hbWVGb3JQcmludGluZzogc2VsZWN0ZWRSb3cubmFtZUZvclByaW50aW5nLFxyXG4gICAgICAgIG5hbWVGb3JQcmludGluZ0ZsYWc6IHNlbGVjdGVkUm93Lm5hbWVGb3JQcmludGluZywgLy9UT0RPXHJcbiAgICAgICAgY29tbWVudHM6IHNlbGVjdGVkUm93LmNvbW1lbnRzLFxyXG4gICAgICAgIHVwZGF0ZUZsYWc6IDEsXHJcbiAgICAgIH0pO1xyXG4gICAgfTtcclxuXHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1yb3cgZ2FwLTQganVzdGlmeS1jZW50ZXIgdGV4dC1za2luLXByaW1hcnlcIj5cclxuICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVFZGl0fVxyXG4gICAgICAgICAgY2xhc3NOYW1lPXtgJHtyZXF1ZXN0U3RhdHVzICE9PSA1IHx8ICFyZXF1ZXN0U3RhdHVzID8gXCJoaWRkZW5cIiA6IFwiXCJ9YH1cclxuICAgICAgICA+XHJcbiAgICAgICAgICA8Rm9udEF3ZXNvbWVJY29uIGljb249e2ZhUGVuVG9TcXVhcmV9IC8+XHJcbiAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgb25DbGljaz17aGFuZGxlRGVsZXRlfVxyXG4gICAgICAgICAgY2xhc3NOYW1lPXtgJHtcclxuICAgICAgICAgICAgcmVxdWVzdFN0YXR1cyAhPT0gNSB8fCAhcmVxdWVzdFN0YXR1cyA/IFwiaGlkZGVuXCIgOiBcIlwiXHJcbiAgICAgICAgICB9IHRleHQtcmVkLTUwMGB9XHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgPEZvbnRBd2Vzb21lSWNvbiBpY29uPXtmYVRyYXNofSAvPlxyXG4gICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgIDxidXR0b24+XHJcbiAgICAgICAgICA8Rm9udEF3ZXNvbWVJY29uXHJcbiAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVJvd1VwZGF0ZSg0KX1cclxuICAgICAgICAgICAgaWNvbj17ZmFDaXJjbGVDaGVja31cclxuICAgICAgICAgICAgY2xhc3NOYW1lPXtgJHtcclxuICAgICAgICAgICAgICByZXF1ZXN0U3RhdHVzICE9PSAxIHx8ICFyZXF1ZXN0U3RhdHVzID8gXCJoaWRkZW5cIiA6IFwiXCJcclxuICAgICAgICAgICAgfSB0ZXh0LWdyZWVuLTUwMGB9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgIDxidXR0b24+XHJcbiAgICAgICAgICA8Rm9udEF3ZXNvbWVJY29uXHJcbiAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVJvd1VwZGF0ZSg0KX1cclxuICAgICAgICAgICAgaWNvbj17ZmFYbWFya0NpcmNsZX1cclxuICAgICAgICAgICAgY2xhc3NOYW1lPXtgJHtcclxuICAgICAgICAgICAgICByZXF1ZXN0U3RhdHVzICE9PSAxIHx8ICFyZXF1ZXN0U3RhdHVzID8gXCJoaWRkZW5cIiA6IFwiXCJcclxuICAgICAgICAgICAgfSB0ZXh0LXJlZC01MDBgfVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICA8YnV0dG9uPlxyXG4gICAgICAgICAgPEZvbnRBd2Vzb21lSWNvblxyXG4gICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVSb3dVcGRhdGUoNCl9XHJcbiAgICAgICAgICAgIGljb249e2ZhU3F1YXJlQ2hlY2t9XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT17YCR7XHJcbiAgICAgICAgICAgICAgcmVxdWVzdFN0YXR1cyAhPT0gMiB8fCAhcmVxdWVzdFN0YXR1cyA/IFwiaGlkZGVuXCIgOiBcIlwiXHJcbiAgICAgICAgICAgIH0gdGV4dC1ncmVlbi01MDBgfVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgPC9kaXY+XHJcbiAgICApO1xyXG4gIH07XHJcblxyXG4gIC8vIENvbHVtbiBkZWZpbml0aW9ucyBmb3IgdGhlIGdyaWRcclxuICBjb25zdCBjb2x1bW5EZWZzID0gW1xyXG4gICAge1xyXG4gICAgICBoZWFkZXJOYW1lOiBcIlByb2R1Y3RcIixcclxuICAgICAgZmllbGQ6IFwicHJvZHVjdC5sYWJlbFwiLFxyXG4gICAgICBmbGV4OiAyLFxyXG4gICAgICBoZWFkZXJDbGFzczogXCJoZWFkZXItd2l0aC1ib3JkZXJcIixcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGhlYWRlck5hbWU6IFwiU2l6ZVwiLFxyXG4gICAgICBmaWVsZDogXCJzaXplLmxhYmVsXCIsXHJcbiAgICAgIGZsZXg6IDEsXHJcbiAgICAgIGhlYWRlckNsYXNzOiBcImhlYWRlci13aXRoLWJvcmRlclwiLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgaGVhZGVyTmFtZTogXCJRdWFudGl0eVwiLFxyXG4gICAgICBmaWVsZDogXCJxdWFudGl0eVwiLFxyXG4gICAgICBmbGV4OiAxLFxyXG4gICAgICBoZWFkZXJDbGFzczogXCJoZWFkZXItd2l0aC1ib3JkZXJcIixcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGhlYWRlck5hbWU6IFwiTmFtZSBmb3IgUHJpbnRpbmdcIixcclxuICAgICAgZmllbGQ6IFwibmFtZUZvclByaW50aW5nXCIsXHJcbiAgICAgIGZsZXg6IDIsXHJcbiAgICAgIGhlYWRlckNsYXNzOiBcImhlYWRlci13aXRoLWJvcmRlclwiLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgaGVhZGVyTmFtZTogXCJDb21tZW50c1wiLFxyXG4gICAgICBmaWVsZDogXCJjb21tZW50c1wiLFxyXG4gICAgICBmbGV4OiAyLFxyXG4gICAgICBoZWFkZXJDbGFzczogXCJoZWFkZXItd2l0aC1ib3JkZXJcIixcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGhlYWRlck5hbWU6IFwiQWN0aW9uc1wiLFxyXG4gICAgICBjZWxsUmVuZGVyZXI6IEljb25zUmVuZGVyZXIsXHJcbiAgICAgIGhlYWRlckNsYXNzOiBcImhlYWRlci13aXRoLWJvcmRlclwiLFxyXG4gICAgICBmbGV4OiAxLFxyXG4gICAgICBjZWxsU3R5bGU6IHsganVzdGlmeUNvbnRlbnQ6IFwiY2VudGVyXCIsIHBhZGRpbmdSaWdodDogXCIxMHB4XCIgfSxcclxuICAgIC8vICAgaGlkZTpcclxuICAgIC8vICAgICByZXF1ZXN0U3RhdHVzID09IDIgfHxcclxuICAgIC8vICAgICByZXF1ZXN0U3RhdHVzID09IDMgfHxcclxuICAgIC8vICAgICByZXF1ZXN0U3RhdHVzID09PSAxIHx8XHJcbiAgICAvLyAgICAgcmVxdWVzdFN0YXR1cyA9PSA0LCAvLyBjb2x1bW4gd29uJ3Qgc2hvdyBhdCBhbGxcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGhlYWRlck5hbWU6IFwiU3RhdHVzXCIsXHJcbiAgICAgIGZpZWxkOiBcInN0YXR1c1wiLFxyXG4gICAgICBoaWRlOiByZXF1ZXN0U3RhdHVzID09PSA1IHx8ICFyZXF1ZXN0U3RhdHVzLCAvLyBjb2x1bW4gd29uJ3Qgc2hvdyBhdCBhbGxcclxuICAgICAgY2VsbFJlbmRlcmVyOiBwcm9kdWN0U3RhdHVzUmVuZGVyZXIsXHJcbiAgICAgIGNlbGxTdHlsZTogKCkgPT4gKHsganVzdGlmeUNvbnRlbnQ6IFwiY2VudGVyXCIgfSksXHJcbiAgICAgIGZsZXg6IDEuMjUsXHJcbiAgICAgIGZpbHRlclBhcmFtczoge1xyXG4gICAgICAgIHZhbHVlczogW1wiUGVuZGluZyBSZXZpZXdcIiwgXCJBcHByb3ZlZFwiLCBcIlJlamVjdGVkXCIsIFwiT3JkZXJlZFwiLCBcIkRyYWZ0XCJdLFxyXG4gICAgICB9LFxyXG4gICAgfSxcclxuICBdO1xyXG4gIC8vI2VuZHJlZ2lvbiBhZy1ncmlkXHJcblxyXG4gIC8vI3JlZ2lvbiBhcGknc1xyXG4gIGZ1bmN0aW9uIGdldEFsbFByb2R1Y3RzKCkge1xyXG4gICAgbGV0IHNlcnZlckFkZHJlc3MgPSBhcGlDb25maWcuc2VydmVyQWRkcmVzcztcclxuICAgIHJldHVybiBmZXRjaChgJHtzZXJ2ZXJBZGRyZXNzfXBwZS1jb25zdW1hYmxlcy9hbGwtcHJvZHVjdHNgLCB7XHJcbiAgICAgIG1ldGhvZDogXCJHRVRcIixcclxuICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxyXG4gICAgICB9LFxyXG4gICAgICBjcmVkZW50aWFsczogXCJpbmNsdWRlXCIsXHJcbiAgICB9KVxyXG4gICAgICAudGhlbihhc3luYyAocmVzKSA9PiB7XHJcbiAgICAgICAgaWYgKHJlcy5zdGF0dXMgPT0gNTAyKSB7XHJcbiAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChyZXMuc3RhdHVzID09PSAyMDApIHtcclxuICAgICAgICAgIHJldHVybiByZXMuanNvbigpO1xyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAocmVzLnN0YXR1cyA9PT0gNDAwKSB7XHJcbiAgICAgICAgICB0b2FzdC5lcnJvcihcclxuICAgICAgICAgICAgXCJUaGVyZSB3YXMgYW4gZXJyb3Igd2l0aCB5b3VyIHJlcXVlc3QuIFBsZWFzZSBjaGVjayB5b3VyIGRhdGEgYW5kIHRyeSBhZ2Fpbi5cIlxyXG4gICAgICAgICAgKTtcclxuICAgICAgICB9IGVsc2UgaWYgKHJlcy5zdGF0dXMgPT09IDQwMSkge1xyXG4gICAgICAgICAgdG9hc3QuZXJyb3IoXCJZb3VyIHNlc3Npb24gaGFzIGV4cGlyZWQuIFBsZWFzZSBsb2cgaW4gYWdhaW4uXCIpO1xyXG4gICAgICAgICAgc2V0VGltZW91dChhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgICAgIGF3YWl0IGxvZ291dCgpO1xyXG4gICAgICAgICAgICByb3V0ZXIucHVzaChcIi9sb2dpblwiKTtcclxuICAgICAgICAgIH0sIDMwMDApO1xyXG4gICAgICAgIH1cclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJGYWlsZWQgdG8gZmV0Y2ggcHJvZHVjdHNcIik7XHJcbiAgICAgIH0pXHJcbiAgICAgIC5jYXRjaCgoZXJyb3IpID0+IHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKGVycm9yKTtcclxuICAgICAgICB0b2FzdC5lcnJvcihgRmFpbGVkIHRvIGZldGNoIHByb2R1Y3RzOiAke2Vycm9yLm1lc3NhZ2V9YCk7XHJcbiAgICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICAgIH0pO1xyXG4gIH1cclxuICBmdW5jdGlvbiBnZXRBbGxTaXRlcygpIHtcclxuICAgIGxldCBzZXJ2ZXJBZGRyZXNzID0gYXBpQ29uZmlnLnNlcnZlckFkZHJlc3M7XHJcbiAgICByZXR1cm4gZmV0Y2goYCR7c2VydmVyQWRkcmVzc31wcGUtY29uc3VtYWJsZXMvc2l0ZXNgLCB7XHJcbiAgICAgIG1ldGhvZDogXCJHRVRcIixcclxuICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxyXG4gICAgICB9LFxyXG4gICAgICBjcmVkZW50aWFsczogXCJpbmNsdWRlXCIsXHJcbiAgICB9KS50aGVuKGFzeW5jIChyZXMpID0+IHtcclxuICAgICAgaWYgKHJlcy5zdGF0dXMgPT0gNTAyKSB7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcbiAgICAgIGlmIChyZXMuc3RhdHVzID09PSAyMDApIHtcclxuICAgICAgICByZXR1cm4gcmVzLmpzb24oKTtcclxuICAgICAgfVxyXG4gICAgICBpZiAocmVzLnN0YXR1cyA9PT0gNDAwKSB7XHJcbiAgICAgICAgdG9hc3QuZXJyb3IoXHJcbiAgICAgICAgICBcIlRoZXJlIHdhcyBhbiBlcnJvciB3aXRoIHlvdXIgcmVxdWVzdC4gUGxlYXNlIGNoZWNrIHlvdXIgZGF0YSBhbmQgdHJ5IGFnYWluLlwiXHJcbiAgICAgICAgKTtcclxuICAgICAgfSBlbHNlIGlmIChyZXMuc3RhdHVzID09PSA0MDEpIHtcclxuICAgICAgICB0b2FzdC5lcnJvcihcIllvdXIgc2Vzc2lvbiBoYXMgZXhwaXJlZC4gUGxlYXNlIGxvZyBpbiBhZ2Fpbi5cIik7XHJcbiAgICAgICAgc2V0VGltZW91dChhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgICBhd2FpdCBsb2dvdXQoKTtcclxuICAgICAgICAgIHJvdXRlci5wdXNoKFwiL2xvZ2luXCIpO1xyXG4gICAgICAgIH0sIDMwMDApO1xyXG4gICAgICB9XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcihcIkZhaWxlZCB0byBmZXRjaCBwcm9kdWN0c1wiKTtcclxuICAgIH0pO1xyXG4gIH1cclxuICAvLyNlbmRyZWdpb24gYXBpJ3NcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgZmV0Y2hTaXRlcyA9IGFzeW5jICgpID0+IHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zdCBzaXRlRGF0YSA9IGF3YWl0IGdldEFsbFNpdGVzKCk7XHJcbiAgICAgICAgY29uc3QgZm9ybWF0dGVkU2l0ZXMgPSBzaXRlRGF0YT8ubWFwKChzaXRlKSA9PiAoe1xyXG4gICAgICAgICAgdmFsdWU6IHNpdGUuaWQsXHJcbiAgICAgICAgICBsYWJlbDogc2l0ZS5uYW1lLFxyXG4gICAgICAgIH0pKTtcclxuICAgICAgICBzZXRTaXRlRGF0YShmb3JtYXR0ZWRTaXRlcyk7XHJcbiAgICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGZldGNoaW5nIFNpdGVzXCIsIGVycm9yKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuICAgIGNvbnN0IGZldGNoUHJvZHVjdHMgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3QgcHJvZHVjdHNKc29uID0gYXdhaXQgZ2V0QWxsUHJvZHVjdHMoKTtcclxuICAgICAgICBjb25zdCBmaWx0ZXJPdXRIaWRkZW5Qcm9kdWN0cyA9IHByb2R1Y3RzSnNvbi5maWx0ZXIoXHJcbiAgICAgICAgICAocHJvZHVjdCkgPT4gIXByb2R1Y3QuSXNQcm9kdWN0SGlkZGVuXHJcbiAgICAgICAgKTtcclxuICAgICAgICBjb25zdCBmb3JtYXR0ZWRQcm9kdWN0cyA9IGZpbHRlck91dEhpZGRlblByb2R1Y3RzPy5tYXAoKHByb2R1Y3QpID0+ICh7XHJcbiAgICAgICAgICB2YWx1ZTogcHJvZHVjdC5Qcm9kdWN0SWQsXHJcbiAgICAgICAgICBsYWJlbDogcHJvZHVjdC5Qcm9kdWN0TmFtZSxcclxuICAgICAgICAgIHNpemVfcmVxdWlyZWQ6IHByb2R1Y3QuSXNTaXplUmVxdWlyZWQgfHwgcHJvZHVjdC5zaXplX3JlcXVpcmVkLFxyXG4gICAgICAgICAgc2l6ZXM6IHByb2R1Y3QuQXZhaWxhYmxlU2l6ZXNcclxuICAgICAgICAgICAgPyBwcm9kdWN0LkF2YWlsYWJsZVNpemVzLnNwbGl0KFwiLCBcIikubWFwKChzaXplKSA9PiAoe1xyXG4gICAgICAgICAgICAgICAgdmFsdWU6IHNpemUudG9Mb3dlckNhc2UoKS5yZXBsYWNlKC9cXHMrL2csIFwiXCIpLFxyXG4gICAgICAgICAgICAgICAgbGFiZWw6IHNpemUsXHJcbiAgICAgICAgICAgICAgfSkpXHJcbiAgICAgICAgICAgIDogW10sXHJcbiAgICAgICAgICBwcm9kdWN0Q29kZTogcHJvZHVjdC5Qcm9kdWN0Q29kZSxcclxuICAgICAgICAgIHBhY2thZ2VRdWFudGl0eTogcHJvZHVjdC5QYWNrYWdlUXVhbnRpdHksXHJcbiAgICAgICAgICBwcm9kdWN0VHlwZTogcHJvZHVjdC5Qcm9kdWN0VHlwZSxcclxuICAgICAgICAgIG5hbWVfcHJpbnRhYmxlOiBwcm9kdWN0Lm5hbWVfcHJpbnRhYmxlLFxyXG4gICAgICAgIH0pKTtcclxuXHJcbiAgICAgICAgc2V0UHJvZHVjdHNEYXRhKGZvcm1hdHRlZFByb2R1Y3RzKTtcclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZmV0Y2hpbmcgcHJvZHVjdHM6XCIsIGVycm9yKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICBmZXRjaFByb2R1Y3RzKCk7XHJcbiAgICBmZXRjaFNpdGVzKCk7XHJcbiAgICBzZXRMb2FkaW5nKGZhbHNlKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIC8vI3JlZ2lvbiBWYWxpZGF0ZVxyXG4gIC8vIFVwZGF0ZSB0aGUgdmFsaWRhdGUgZnVuY3Rpb24gdG8gdXNlIHNhdmVkSXRlbXMgaW5zdGVhZCBvZiBpdGVtc1xyXG4gIGNvbnN0IHZhbGlkYXRlID0gKCkgPT4ge1xyXG4gICAgY29uc3QgbmV3RXJyb3JzID0ge307XHJcblxyXG4gICAgaWYgKCFzaXRlKSBuZXdFcnJvcnMuc2l0ZSA9IFwiU2l0ZSBpcyByZXF1aXJlZC5cIjtcclxuICAgIGlmICghcmVxdWlyZWREYXRlKSBuZXdFcnJvcnMucmVxdWlyZWREYXRlID0gXCJSZXF1aXJlZCBkYXRlIGlzIG1pc3NpbmcuXCI7XHJcbiAgICBpZiAoIXNhdmVkSXRlbXMgfHwgc2F2ZWRJdGVtcy5sZW5ndGggPT09IDApIHtcclxuICAgICAgbmV3RXJyb3JzLnNhdmVkSXRlbXMgPSBcIkF0IGxlYXN0IG9uZSBpdGVtIG11c3QgYmUgYWRkZWQuXCI7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gVXBkYXRlIHN0YXRlIGZvciBmaWVsZC1sZXZlbCBtZXNzYWdlc1xyXG4gICAgc2V0RXJyb3JzKG5ld0Vycm9ycyk7XHJcblxyXG4gICAgY29uc3QgaGFzRXJyb3JzID0gT2JqZWN0LmtleXMobmV3RXJyb3JzKS5sZW5ndGggPiAwO1xyXG4gICAgaWYgKGhhc0Vycm9ycykge1xyXG4gICAgICBjb25zdCBlcnJvck1lc3NhZ2VzID0gT2JqZWN0LnZhbHVlcyhuZXdFcnJvcnMpO1xyXG5cclxuICAgICAgLy8gc2V0UG9wdXBDb25maWcoe1xyXG4gICAgICAvLyAgIHRpdGxlOiBcIlZhbGlkYXRpb24gRXJyb3JcIixcclxuICAgICAgLy8gICBtZXNzYWdlOiBlcnJvck1lc3NhZ2VzLmpvaW4oXCJcXG5cIiksXHJcbiAgICAgIC8vICAgdHlwZTogXCJlcnJvclwiLFxyXG4gICAgICAvLyB9KTtcclxuICAgICAgLy8gc2V0U2hvd1BvcHVwKHRydWUpOyAvLyB0cmlnZ2VyIHBvcHVwXHJcbiAgICAgIGNvbnN0IGVycm9yc0xlbmd0aCA9IE9iamVjdC5rZXlzKG5ld0Vycm9ycykubGVuZ3RoO1xyXG4gICAgICBpZiAoZXJyb3JzTGVuZ3RoID4gMSkge1xyXG4gICAgICAgIHRvYXN0LmVycm9yKFwiUGxlYXNlIGZpbGwgYWxsIHJlcXVpcmVkIGZpZWxkcy5cIik7XHJcbiAgICAgIH0gZWxzZSBpZiAoZXJyb3JzTGVuZ3RoID09IDEgJiYgbmV3RXJyb3JzLnNhdmVkSXRlbXMpIHtcclxuICAgICAgICB0b2FzdC5lcnJvcihcIlBsZWFzZSBhZGQgYXQgbGVhc3Qgb25lIGl0ZW0uXCIpO1xyXG4gICAgICB9XHJcbiAgICAgIHJldHVybiBmYWxzZTtcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gdHJ1ZTtcclxuICB9O1xyXG4gIGNvbnN0IGhhbmRsZVNpdGVBZGQgPSAodmFsdWUpID0+IHtcclxuICAgIHNldFNpdGUodmFsdWUpO1xyXG5cclxuICAgIC8vIENsZWFyIHRoZSBlcnJvciBmb3Igc2l0ZSBpZiB2YWx1ZSBpcyB2YWxpZFxyXG4gICAgc2V0RXJyb3JzKChwcmV2KSA9PiB7XHJcbiAgICAgIGNvbnN0IHVwZGF0ZWQgPSB7IC4uLnByZXYgfTtcclxuICAgICAgaWYgKHZhbHVlKSB7XHJcbiAgICAgICAgZGVsZXRlIHVwZGF0ZWQuc2l0ZTtcclxuICAgICAgfVxyXG4gICAgICByZXR1cm4gdXBkYXRlZDtcclxuICAgIH0pO1xyXG4gIH07XHJcblxyXG4gIC8vIEFkZCBkYXRlIHZhbGlkYXRpb24gZnVuY3Rpb25cclxuICBjb25zdCB2YWxpZGF0ZVJlcXVpcmVkRGF0ZSA9IChzZWxlY3RlZERhdGUpID0+IHtcclxuICAgIC8vIHNldFJlcXVpcmVkRGF0ZShzZWxlY3RlZERhdGUpO1xyXG5cclxuICAgIHNldEVycm9ycygocHJldikgPT4ge1xyXG4gICAgICBjb25zdCB1cGRhdGVkID0geyAuLi5wcmV2IH07XHJcbiAgICAgIGlmIChzZWxlY3RlZERhdGUpIHtcclxuICAgICAgICBkZWxldGUgdXBkYXRlZC5yZXF1aXJlZERhdGU7XHJcbiAgICAgIH1cclxuICAgICAgcmV0dXJuIHVwZGF0ZWQ7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpZiAoIXNlbGVjdGVkRGF0ZSkge1xyXG4gICAgICBzZXREYXRlV2FybmluZyhcIlwiKTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IHRvZGF5ID0gbmV3IERhdGUoKTtcclxuICAgIGNvbnN0IHNlbGVjdGVkID0gbmV3IERhdGUoc2VsZWN0ZWREYXRlKTtcclxuICAgIGNvbnN0IHR3b1dlZWtzRnJvbU5vdyA9IG5ldyBEYXRlKCk7XHJcbiAgICB0d29XZWVrc0Zyb21Ob3cuc2V0RGF0ZSh0b2RheS5nZXREYXRlKCkgKyAxNCk7XHJcblxyXG4gICAgaWYgKHNlbGVjdGVkIDwgdHdvV2Vla3NGcm9tTm93KSB7XHJcbiAgICAgIHNldERhdGVXYXJuaW5nKFxyXG4gICAgICAgIFwiTm90aWNlOiBMZXNzIHRoYW4gMiB3ZWVrcyBsZWFkIHRpbWUgbWF5IGFmZmVjdCBhdmFpbGFiaWxpdHkuXCJcclxuICAgICAgKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHNldERhdGVXYXJuaW5nKFwiXCIpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vICNyZWdpb24gaGFuZGxlcnNcclxuXHJcbiAgY29uc3QgdHJpbUlucHV0VGV4dCA9IChpbnB1dCkgPT4ge1xyXG4gICAgcmV0dXJuIGlucHV0LnRyaW0oKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVDYW5jZWxSZWFzb24gPSAoZGF0YSkgPT4ge1xyXG4gICAgaWYgKGRhdGEpIHtcclxuICAgICAgc2V0SXNWYWxpZENhbmNlbFJlYXNvbih0cnVlKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHNldElzVmFsaWRDYW5jZWxSZWFzb24oZmFsc2UpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZVNhdmVDbGljayA9IChlKSA9PiB7XHJcbiAgICAvLyBlLnByZXZlbnREZWZhdWx0KCk7XHJcbiAgICBpZiAoaGFzVW5zYXZlZENoYW5nZXMpIHtcclxuICAgICAgc2V0UG9wdXBUeXBlKFwidW5zYXZlZFdhcm5pbmdcIik7XHJcbiAgICAgIHNldElzT3Blbih0cnVlKTsgLy8geW91ciBleGlzdGluZyBtb2RhbCBzdGF0ZVxyXG4gICAgfSBlbHNlIHtcclxuICAgICAgc2F2ZUN1cnJlbnRJdGVtKGUpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IHNhdmVDdXJyZW50SXRlbSA9IChlKSA9PiB7XHJcbiAgICBjb25zb2xlLmxvZyhcInNhdmUgb3JkZXIgcmVxdWVzdFwiKTtcclxuICAgIHNldEVycm9ycygocHJldikgPT4ge1xyXG4gICAgICBjb25zdCB1cGRhdGVkID0geyAuLi5wcmV2IH07XHJcbiAgICAgIGlmIChlKSB7XHJcbiAgICAgICAgZGVsZXRlIHVwZGF0ZWQuc2F2ZWRJdGVtcztcclxuICAgICAgfVxyXG4gICAgICByZXR1cm4gdXBkYXRlZDtcclxuICAgIH0pO1xyXG5cclxuICAgIGlmICghZSkge1xyXG4gICAgICBjb25zb2xlLmxvZyhcInJldHVybmluZyBoZXJlXCIpO1xyXG4gICAgICBzZXREYXRlV2FybmluZyhcIlwiKTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xyXG4gICAgbGV0IGNvdW50ID0gMDtcclxuXHJcbiAgICBpZiAoIWN1cnJlbnRJdGVtLnByb2R1Y3QpIHtcclxuICAgICAgY29uc29sZS5sb2coXCIrKyBwcm9kdWN0XCIpO1xyXG4gICAgICBjb3VudCsrO1xyXG4gICAgICAvLyBzZXRQcm9kdWN0VmFsaWQoXCJQbGVhc2Ugc2VsZWN0IGEgcHJvZHVjdFwiKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIC8vIHNldFByb2R1Y3RWYWxpZChcIlwiKTtcclxuICAgIH1cclxuXHJcbiAgICBpZiAoIWN1cnJlbnRJdGVtLnF1YW50aXR5IHx8IGN1cnJlbnRJdGVtLnF1YW50aXR5IDwgMSkge1xyXG4gICAgICBjb25zb2xlLmxvZyhcIisrIHF1YW50aXR5XCIpO1xyXG4gICAgICBjb3VudCsrO1xyXG4gICAgICAvLyBzZXRRdWFudGl0eVZhbGlkKFwiUGxlYXNlIGVudGVyIGEgdmFsaWQgcXVhbnRpdHlcIik7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBpZiAoY3VycmVudEl0ZW0ucXVhbnRpdHkgPiAxICYmIGN1cnJlbnRJdGVtLm5hbWVGb3JQcmludGluZykge1xyXG4gICAgICAgIC8vIGN1cnJlbnRJdGVtLm5hbWVGb3JQcmludGluZz1cIlwiXHJcbiAgICAgICAgc2V0Q3VycmVudEl0ZW0oKHByZXYpID0+ICh7IC4uLnByZXYsIG5hbWVGb3JQcmludGluZzogXCJcIiB9KSk7XHJcbiAgICAgIH1cclxuICAgICAgLy8gc2V0UXVhbnRpdHlWYWxpZChcIlwiKTtcclxuICAgIH1cclxuXHJcbiAgICBpZiAoXHJcbiAgICAgIGN1cnJlbnRJdGVtLnByb2R1Y3Q/LnNpemVfcmVxdWlyZWQgJiZcclxuICAgICAgY3VycmVudEl0ZW0ucHJvZHVjdD8uc2l6ZXM/Lmxlbmd0aCAmJlxyXG4gICAgICAhY3VycmVudEl0ZW0uc2l6ZVxyXG4gICAgKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwiKysgcHJvZHVjdCBzaXplXCIpO1xyXG4gICAgICBjb3VudCsrO1xyXG4gICAgICAvLyBBZGQgc2l6ZSB2YWxpZGF0aW9uIGlmIG5lZWRlZFxyXG4gICAgfVxyXG5cclxuICAgIGlmIChjb3VudCA+IDApIHtcclxuICAgICAgdG9hc3QuZXJyb3IoXHJcbiAgICAgICAgXCJQbGVhc2UgZmlsbCBpbiBhbGwgbWFuZGF0b3J5IGZpZWxkcyB0byBhZGQgaXRlbXMgd2l0aCB2YWxpZCBxdWFudGl0eVwiXHJcbiAgICAgICk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuICAgIC8vICAgICBpZiAoZWRpdGluZ1Jvd0lkKSB7XHJcbiAgICAvLyAgICAgICAgIC8vIFVwZGF0ZSBleGlzdGluZyBpdGVtXHJcbiAgICAvLyAgIGNvbnN0IHVwZGF0ZWRJdGVtcyA9IHNhdmVkSXRlbXMubWFwKGl0ZW0gPT5cclxuICAgIC8vICAgICBpdGVtLmlkID09PSBlZGl0aW5nUm93SWQgPyB7IC4uLmN1cnJlbnRJdGVtLCBpZDogZWRpdGluZ1Jvd0lkIH0gOiBpdGVtXHJcbiAgICAvLyAgICk7XHJcbiAgICAvLyAgIHNldFNhdmVkSXRlbXModXBkYXRlZEl0ZW1zKTtcclxuICAgIC8vICAgc2V0RWRpdGluZ1Jvd0lkKG51bGwpO1xyXG4gICAgLy8gfSBlbHNlIHtcclxuICAgIC8vICAgLy8gQWRkIG5ldyBpdGVtXHJcbiAgICAvLyAgIHNldFNhdmVkSXRlbXMoWy4uLnNhdmVkSXRlbXMsIHsgLi4uY3VycmVudEl0ZW0sIGlkOiBEYXRlLm5vdygpIH1dKTtcclxuXHJcbiAgICAvLyB9XHJcbiAgICBpZiAoZWRpdGluZ1Jvd0lkKSB7XHJcbiAgICAgIC8vIEFkZCB0aGUgZWRpdGVkIGl0ZW0gYmFjayB0byBzYXZlZEl0ZW1zXHJcbiAgICAgIHNldFNhdmVkSXRlbXMoW1xyXG4gICAgICAgIC4uLnNhdmVkSXRlbXMsXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgLi4uY3VycmVudEl0ZW0sXHJcbiAgICAgICAgICBpZDogZWRpdGluZ1Jvd0lkLFxyXG4gICAgICAgICAgcHJvZHVjdDogY3VycmVudEl0ZW0ucHJvZHVjdCA/IHsgLi4uY3VycmVudEl0ZW0ucHJvZHVjdCB9IDogbnVsbCxcclxuICAgICAgICAgIHNpemU6IGN1cnJlbnRJdGVtLnNpemUgPyB7IC4uLmN1cnJlbnRJdGVtLnNpemUgfSA6IG51bGwsXHJcbiAgICAgICAgfSxcclxuICAgICAgXSk7XHJcbiAgICAgIHNldEVkaXRpbmdSb3dJZChudWxsKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIC8vIEFkZCBuZXcgaXRlbVxyXG4gICAgICBzZXRTYXZlZEl0ZW1zKFtcclxuICAgICAgICAuLi5zYXZlZEl0ZW1zLFxyXG4gICAgICAgIHsgLi4uY3VycmVudEl0ZW0sIGlkOiBgcm93LSR7RGF0ZS5ub3coKX1gIH0sXHJcbiAgICAgIF0pO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIFJlc2V0IGN1cnJlbnQgaXRlbVxyXG4gICAgc2V0Q3VycmVudEl0ZW0oeyAuLi5lbXB0eUl0ZW0gfSk7XHJcbiAgICBzZXRFZGl0aW5nUm93SWQobnVsbCk7XHJcbiAgICBzZXRIYXNVbnNhdmVkQ2hhbmdlcyhmYWxzZSk7XHJcbiAgfTtcclxuXHJcbiAgLy8gSGFuZGxlIGN1cnJlbnQgaXRlbSBjaGFuZ2VzXHJcbiAgY29uc3QgaGFuZGxlQ3VycmVudEl0ZW1DaGFuZ2UgPSAoZmllbGQsIHZhbHVlKSA9PiB7XHJcbiAgICBpZiAoZmllbGQgPT09IFwicHJvZHVjdFwiKSB7XHJcbiAgICAgIC8vIGNvbnNvbGUubG9nKFwiZmllbGQtLS0tLS0tLS0tLS0tXCIsdmFsdWUpXHJcbiAgICAgIC8vIFJlc2V0IG90aGVyIGZpZWxkcyB3aGVuIHByb2R1Y3QgY2hhbmdlc1xyXG4gICAgICBzZXRDdXJyZW50SXRlbSgocHJldikgPT4gKHtcclxuICAgICAgICAuLi5wcmV2LFxyXG4gICAgICAgIFtmaWVsZF06IHZhbHVlLFxyXG4gICAgICAgIHNpemU6IG51bGwsIC8vIFJlc2V0IHNpemUgc2luY2UgZGlmZmVyZW50IHByb2R1Y3RzIGhhdmUgZGlmZmVyZW50IGF2YWlsYWJsZSBzaXplc1xyXG4gICAgICAgIHF1YW50aXR5OiBcIlwiLCAvLyBSZXNldCBxdWFudGl0eVxyXG4gICAgICAgIG5hbWVGb3JQcmludGluZzogXCJcIiwgLy8gUmVzZXQgbmFtZSBmb3IgcHJpbnRpbmdcclxuICAgICAgICBuYW1lRm9yUHJpbnRpbmdGbGFnOiBcIlwiLCAvLyBSZXNldCBuYW1lIGZvciBwcmludGluZ1xyXG4gICAgICAgIGNvbW1lbnRzOiBcIlwiLCAvLyBSZXNldCBjb21tZW50c1xyXG4gICAgICB9KSk7XHJcbiAgICB9IGVsc2UgaWYgKGZpZWxkID09PSBcInF1YW50aXR5XCIgJiYgdmFsdWUgPiAxKSB7XHJcbiAgICAgIHNldEN1cnJlbnRJdGVtKChwcmV2KSA9PiAoe1xyXG4gICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgW2ZpZWxkXTogdmFsdWUsXHJcbiAgICAgICAgbmFtZUZvclByaW50aW5nOiBcIlwiLFxyXG4gICAgICB9KSk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBzZXRDdXJyZW50SXRlbSgocHJldikgPT4gKHsgLi4ucHJldiwgW2ZpZWxkXTogdmFsdWUgfSkpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vI3JlZ2lvbiBTYXZlXHJcbiAgY29uc3QgaGFuZGxlU3VibWl0ID0gKHN0YXR1c19JRCkgPT4ge1xyXG4gICAgaWYgKHN0YXR1c19JRCAhPSAxICYmICF2YWxpZGF0ZSgpKSB7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuICAgIC8vIGNvbnNvbGUubG9nKFwic3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3NcIixzYXZlZEl0ZW1zKTtcclxuICAgIHNldExvYWRpbmcodHJ1ZSk7XHJcbiAgICBsZXQgc2VydmVyQWRkcmVzcyA9IGFwaUNvbmZpZy5zZXJ2ZXJBZGRyZXNzO1xyXG5cclxuICAgIC8vIFRyYW5zZm9ybSBzYXZlZEl0ZW1zIHRvIEFQSSBmb3JtYXRcclxuICAgIGNvbnN0IGl0ZW1zID0gc2F2ZWRJdGVtcy5tYXAoKGl0ZW0pID0+ICh7XHJcbiAgICAgIHJlcXVlc3RJdGVtSWQ6IGl0ZW0ucmVxdWVzdEl0ZW1JZCxcclxuICAgICAgLy8gUmVxdWVzdEl0ZW1JRDogdHlwZW9mIGl0ZW0uaWQgPT09ICdzdHJpbmcnICYmIGl0ZW0uaWQuc3RhcnRzV2l0aCgncm93LScpID8gbnVsbCA6IGl0ZW0uaWQsXHJcblxyXG4gICAgICBQcm9kdWN0SUQ6IGl0ZW0ucHJvZHVjdD8udmFsdWUsIC8vIFByb2R1Y3RJRFxyXG4gICAgICBQcm9kdWN0TmFtZTogaXRlbS5wcm9kdWN0Py5sYWJlbCB8fCBpdGVtLnByb2R1Y3Q/LnZhbHVlLCAvLyBQcm9kdWN0SURcclxuICAgICAgU2l6ZTogaXRlbS5zaXplPy5sYWJlbCB8fCBudWxsLCAvLyBTaXplIC8vVE9ETyBzaXplIGlkIGhhbmRlbFxyXG4gICAgICBRdWFudGl0eTogTnVtYmVyKGl0ZW0ucXVhbnRpdHkpIHx8IDAsIC8vIFF1YW50aXR5IChtYWtlIHN1cmUgaXQncyBudW1lcmljKVxyXG4gICAgICBOYW1lRm9yUHJpbnRpbmc6IGl0ZW0ubmFtZUZvclByaW50aW5nIHx8IFwiXCIsIC8vIE5hbWVGb3JQcmludGluZ1xyXG4gICAgICBDb21tZW50czogaXRlbS5jb21tZW50cyB8fCBcIlwiLCAvLyBDb21tZW50c1xyXG4gICAgfSkpO1xyXG5cclxuICAgIGNvbnN0IGFwaVBheWxvYWQgPSB7XHJcbiAgICAgIFN0YXR1c19pZDogc3RhdHVzX0lELFxyXG4gICAgICBTdWJtaXR0ZXJVc2VySUQ6IHVzZXJEYXRhPy51c2VyX2lkLFxyXG4gICAgICBUYXJnZXRTaXRlSUQ6IHNpdGU/LnZhbHVlIHx8IDMsIC8vIGZhbGxiYWNrIHRvIDNcclxuICAgICAgUmVxdWlyZWREYXRlOiByZXF1aXJlZERhdGUsIC8vIHNob3VsZCBiZSBcIllZWVktTU0tRERcIlxyXG4gICAgICB1c2VybmFtZTogdXNlckRhdGE/LmVtYWlsIHx8IFwic3lzdGVtXCIsIC8vIGZhbGxiYWNrIHVzZXJuYW1lXHJcbiAgICAgIGl0ZW1zOiBpdGVtcywgLy8gYXJyYXkgb2YgaXRlbXNcclxuICAgICAgb3JpZ25hdG9yRW1haWw6IGVtYWlsT2ZPcmlnaW5hdG9yLFxyXG4gICAgfTtcclxuXHJcbiAgICAvLyBjb25zb2xlLmxvZyhcIml0ZW1zLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cIixpdGVtcylcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBmZXRjaChgJHtzZXJ2ZXJBZGRyZXNzfXBwZS1jb25zdW1hYmxlcy9jcmVhdGUtb3JkZXItcmVxdWVzdGAsIHtcclxuICAgICAgICBtZXRob2Q6IFwiUG9zdFwiLFxyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgIFwiY29udGVudC10eXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgY3JlZGVudGlhbHM6IFwiaW5jbHVkZVwiLFxyXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KGFwaVBheWxvYWQpLFxyXG4gICAgICB9KVxyXG4gICAgICAgIC50aGVuKChyZXMpID0+IHtcclxuICAgICAgICAgIGlmIChyZXMuc3RhdHVzID09PSAyMDApIHtcclxuICAgICAgICAgICAgcmV0dXJuIHJlcy5qc29uKCk7XHJcbiAgICAgICAgICB9IGVsc2UgaWYgKHJlcy5zdGF0dXMgPT09IDQwMSkge1xyXG4gICAgICAgICAgICB0b2FzdC5lcnJvcihcIllvdXIgc2Vzc2lvbiBoYXMgZXhwaXJlZC4gUGxlYXNlIGxvZyBpbiBhZ2Fpbi5cIik7XHJcbiAgICAgICAgICAgIHNldFRpbWVvdXQoYXN5bmMgKCkgPT4ge1xyXG4gICAgICAgICAgICAgIGF3YWl0IGxvZ291dCgpO1xyXG4gICAgICAgICAgICAgIGNvbnN0IHJlZGlyZWN0VXJsID0gYC9sb2dpbj9yZWRpcmVjdD0ke2VuY29kZVVSSUNvbXBvbmVudChcclxuICAgICAgICAgICAgICAgIHdpbmRvdy5sb2NhdGlvbi5wYXRobmFtZVxyXG4gICAgICAgICAgICAgICl9YDtcclxuICAgICAgICAgICAgICByb3V0ZXIucHVzaChyZWRpcmVjdFVybCk7XHJcbiAgICAgICAgICAgIH0sIDMwMDApO1xyXG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcclxuICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIHRvYXN0LmVycm9yKFwiRmFpbGVkIHRvIHNhdmUgcHJvZHVjdC5cIik7XHJcbiAgICAgICAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0pXHJcbiAgICAgICAgLnRoZW4oKGpzb24pID0+IHtcclxuICAgICAgICAgIGlmIChqc29uICYmIGpzb24ubXNnKSB7XHJcbiAgICAgICAgICAgIHRvYXN0LnN1Y2Nlc3MoanNvbi5tc2csIHtcclxuICAgICAgICAgICAgICBwb3NpdGlvbjogXCJ0b3AtcmlnaHRcIixcclxuICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgICAgIHJvdXRlci5yZXBsYWNlKFwiL3BwZS1jb25zdW1hYmxlXCIpO1xyXG4gICAgICAgICAgICB9LCAyMDAwKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9KTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwiZXJyb3JpbiBzdWJtaXR0aW5nIHRoZSBvcmRlclwiLCBlcnJvcik7XHJcbiAgICB9XHJcbiAgICAvLyBzZXRMb2FkaW5nKGZhbHNlKTtcclxuICAgIC8vIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgLy8gICBzZXRMb2FkaW5nKGZhbHNlKTtcclxuICAgIC8vICAgdG9hc3Quc3VjY2VzcyhcIlJlcXVlc3Qgc3VibWl0dGVkIVwiKTtcclxuICAgIC8vICAgcm91dGVyLnB1c2goXCIvcHBlLWNvbnN1bWFibGVcIik7XHJcbiAgICAvLyB9LCAxMDAwKTtcclxuICB9O1xyXG5cclxuICAvLyNyZWdpb24gdXBkYXRlZCBjb25kaXRpb25hbFxyXG4gIGNvbnN0IHVwZGF0ZVNlbGVjdGVkUm93U3RhdHVzID0gKFxyXG4gICAgdXBkYXRlZFN0YXR1c0lELFxyXG4gICAgZXhpc3RpbmdTdGF0dXNJZCxcclxuICAgIGNvbW1lbnRcclxuICApID0+IHtcclxuICAgIGNvbnN0IFNUQVRVU19NQVAgPSB7XHJcbiAgICAgIDE6IFwiUGVuZGluZyBSZXZpZXdcIixcclxuICAgICAgMjogXCJBcHByb3ZlZFwiLFxyXG4gICAgICAzOiBcIlJlamVjdGVkXCIsXHJcbiAgICAgIDQ6IFwiT3JkZXJlZFwiLFxyXG4gICAgICA1OiBcIkRyYWZ0XCIsXHJcbiAgICB9O1xyXG4gICAgY29uc3QgdG9CZVVwZGF0ZWQgPSBzYXZlZEl0ZW1zLmZpbHRlcihcclxuICAgICAgKGl0ZW0pID0+IGl0ZW0uc3RhdHVzID09IFNUQVRVU19NQVBbZXhpc3RpbmdTdGF0dXNJZF1cclxuICAgICk7XHJcblxyXG4gICAgaWYgKHRvQmVVcGRhdGVkLmxlbmd0aCA9PT0gMCkge1xyXG4gICAgICB0b2FzdC53YXJuaW5nKFwiTm8gaXRlbXMgZm91bmQgd2l0aCB0aGUgc3BlY2lmaWVkIHN0YXR1c1wiKTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG5cclxuICAgIHNldExvYWRpbmcodHJ1ZSk7XHJcblxyXG4gICAgaWYgKHVwZGF0ZWRTdGF0dXNJRCA9PSAzKSB7XHJcbiAgICAgIGlmIChjYW5jZWxsZWRSZWFzb25hcGkpIHtcclxuICAgICAgICBzZXRJc1ZhbGlkQ2FuY2VsUmVhc29uKHRydWUpO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKFwidHJpZ2dlcnJlcmVyZXJlcmVkXCIpO1xyXG4gICAgICAgIHNldElzVmFsaWRDYW5jZWxSZWFzb24oZmFsc2UpO1xyXG4gICAgICAgIHJldHVybjtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIGNvbnNvbGUubG9nKFwic3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc1wiLCB0b0JlVXBkYXRlZCk7XHJcbiAgICBjb25zdCB1cGRhdGVQcm9taXNlcyA9IHRvQmVVcGRhdGVkLm1hcCgoaXRlbSkgPT4ge1xyXG4gICAgICBjb25zdCBhcGlQYXlsb2FkID0ge1xyXG4gICAgICAgIHJlcXVlc3Rfbm86IGl0ZW0uaWQsXHJcbiAgICAgICAgUHJvZHVjdE5hbWU6IGl0ZW0ucHJvZHVjdC5sYWJlbCxcclxuICAgICAgICBTaXplOiBpdGVtLnNpemUubGFiZWwsXHJcbiAgICAgICAgUXVhbnRpdHk6IGl0ZW0ucXVhbnRpdHksXHJcbiAgICAgICAgTmFtZUZvclByaW50aW5nOiBpdGVtLm5hbWVGb3JQcmludGluZyxcclxuICAgICAgICBDb21tZW50czogaXRlbS5jb21tZW50cyxcclxuICAgICAgICBSZXF1ZXN0SXRlbUlEOiBpdGVtLmlkLFxyXG4gICAgICAgIGNvbW1lbnRPblVwZGF0aW5nUmVxdWVzdDogY29tbWVudCxcclxuICAgICAgICBhY3Rpb25faWQ6IHVwZGF0ZWRTdGF0dXNJRCxcclxuICAgICAgICBTdWJtaXR0ZXJVc2VySUQ6IHVzZXJEYXRhLnVzZXJfaWQsXHJcbiAgICAgICAgU3VibWl0dGVyRW1haWw6IHVzZXJEYXRhPy5lbWFpbCxcclxuICAgICAgICBvcmlnbmF0b3JFbWFpbDogZW1haWxPZk9yaWdpbmF0b3IsXHJcbiAgICAgICAgY2FuY2VsbGVkUmVhc29uYXBpOiBjYW5jZWxsZWRSZWFzb25hcGksXHJcbiAgICAgIH07XHJcbiAgICAgIGNvbnNvbGUubG9nKFwiYXBpUGF5bG9hZFwiLCBhcGlQYXlsb2FkKTsgLy8gTG9nIGZvciBkZWJ1Z2dpbmcgcHVycG9zZXMuXHJcbiAgICAgIHJldHVybiBmZXRjaChcclxuICAgICAgICBgJHthcGlDb25maWcuc2VydmVyQWRkcmVzc31wcGUtY29uc3VtYWJsZXMvdXBkYXRlLXByb2R1Y3QtcmVxdWVzdC1pdGVtcy8ke2l0ZW0uaWR9YCxcclxuICAgICAgICB7XHJcbiAgICAgICAgICBtZXRob2Q6IFwiUE9TVFwiLFxyXG4gICAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgICBcImNvbnRlbnQtdHlwZVwiOiBcImFwcGxpY2F0aW9uL2pzb25cIixcclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgICBjcmVkZW50aWFsczogXCJpbmNsdWRlXCIsXHJcbiAgICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShhcGlQYXlsb2FkKSxcclxuICAgICAgICB9XHJcbiAgICAgICkudGhlbihhc3luYyAocmVzKSA9PiB7XHJcbiAgICAgICAgaWYgKHJlcy5zdGF0dXMgPT09IDIwMCkge1xyXG4gICAgICAgICAgcmV0dXJuIHJlcy5qc29uKCk7XHJcbiAgICAgICAgfSBlbHNlIGlmIChyZXMuc3RhdHVzID09PSA0MDEpIHtcclxuICAgICAgICAgIHRvYXN0LmVycm9yKFwiWW91ciBzZXNzaW9uIGhhcyBleHBpcmVkLiBQbGVhc2UgbG9nIGluIGFnYWluLlwiKTtcclxuICAgICAgICAgIHNldFRpbWVvdXQoYXN5bmMgKCkgPT4ge1xyXG4gICAgICAgICAgICBhd2FpdCBsb2dvdXQoKTtcclxuICAgICAgICAgICAgY29uc3QgcmVkaXJlY3RVcmwgPSBgL2xvZ2luP3JlZGlyZWN0PSR7ZW5jb2RlVVJJQ29tcG9uZW50KFxyXG4gICAgICAgICAgICAgIHdpbmRvdy5sb2NhdGlvbi5wYXRobmFtZVxyXG4gICAgICAgICAgICApfWA7XHJcbiAgICAgICAgICAgIHJvdXRlci5wdXNoKHJlZGlyZWN0VXJsKTtcclxuICAgICAgICAgIH0sIDMwMDApO1xyXG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiVW5hdXRob3JpemVkXCIpO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICBjb25zdCBlcnJvclRleHQgPSBhd2FpdCByZXMudGV4dCgpO1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcihcIlVwZGF0ZSBmYWlsZWQ6XCIsIGVycm9yVGV4dCk7XHJcbiAgICAgICAgICB0b2FzdC5lcnJvcihcIkZhaWxlZCB0byB1cGRhdGUgaXRlbSBzdGF0dXMuXCIpO1xyXG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiVXBkYXRlIGZhaWxlZFwiKTtcclxuICAgICAgICB9XHJcbiAgICAgIH0pO1xyXG4gICAgfSk7XHJcblxyXG4gICAgUHJvbWlzZS5hbGwodXBkYXRlUHJvbWlzZXMpXHJcbiAgICAgIC50aGVuKChyZXN1bHRzKSA9PiB7XHJcbiAgICAgICAgY29uc3Qgc3VjY2Vzc0NvdW50ID0gcmVzdWx0cy5maWx0ZXIoKHJlc3VsdCkgPT4gcmVzdWx0Py5tc2cpLmxlbmd0aDtcclxuICAgICAgICBpZiAoc3VjY2Vzc0NvdW50ID4gMCkge1xyXG4gICAgICAgICAgdG9hc3Quc3VjY2VzcyhgU3VjY2Vzc2Z1bGx5IHVwZGF0ZWQgJHtzdWNjZXNzQ291bnR9IGl0ZW0ocylgLCB7XHJcbiAgICAgICAgICAgIHBvc2l0aW9uOiBcInRvcC1yaWdodFwiLFxyXG4gICAgICAgICAgfSk7XHJcbiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICAgICAgcm91dGVyLnJlcGxhY2UoXCIvcHBlLWNvbnN1bWFibGVcIik7XHJcbiAgICAgICAgICB9LCAyMDAwKTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgdG9hc3QuZXJyb3IoXCJObyBpdGVtcyB3ZXJlIHN1Y2Nlc3NmdWxseSB1cGRhdGVkXCIpO1xyXG4gICAgICAgICAgcm91dGVyLnB1c2goXCIvcHBlLWNvbnN1bWFibGVcIik7XHJcbiAgICAgICAgfVxyXG4gICAgICB9KVxyXG4gICAgICAuY2F0Y2goKGVycm9yKSA9PiB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIHVwZGF0aW5nIGl0ZW1zOlwiLCBlcnJvcik7XHJcbiAgICAgICAgdG9hc3QuZXJyb3IoXCJTb21lIGl0ZW1zIGZhaWxlZCB0byB1cGRhdGVcIik7XHJcbiAgICAgIH0pXHJcbiAgICAgIC5maW5hbGx5KCgpID0+IHtcclxuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcclxuICAgICAgfSk7XHJcbiAgfTtcclxuICAvLyNlbmRyZWdpb24gdXBkYXRlZCBjb25kaXRpb25hbFxyXG5cclxuICAvLyAjcmVnaW9uIHVwZGF0ZVxyXG4gIGNvbnN0IGhhbmRsZVVwZGF0ZSA9IChhY3Rpb25faWQpID0+IHtcclxuICAgIGlmICghdmFsaWRhdGUoKSkge1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcbiAgICAvLyBoYW5kbGVTYXZlQ2xpY2soKTtcclxuICAgIHNldExvYWRpbmcodHJ1ZSk7XHJcbiAgICBsZXQgc2VydmVyQWRkcmVzcyA9IGFwaUNvbmZpZy5zZXJ2ZXJBZGRyZXNzO1xyXG5cclxuICAgIGNvbnNvbGUubG9nKFwic2F2ZWRJdGVtc1wiLCBzYXZlZEl0ZW1zKTtcclxuICAgIGNvbnN0IGl0ZW1zID0gc2F2ZWRJdGVtcy5tYXAoKGl0ZW0pID0+ICh7XHJcbiAgICAgIC8vIFJlcXVlc3RJdGVtSUQ6IGl0ZW0uaWQsXHJcbiAgICAgIFJlcXVlc3RJdGVtSUQ6XHJcbiAgICAgICAgdHlwZW9mIGl0ZW0uaWQgPT09IFwic3RyaW5nXCIgJiYgaXRlbS5pZC5zdGFydHNXaXRoKFwicm93LVwiKVxyXG4gICAgICAgICAgPyBudWxsXHJcbiAgICAgICAgICA6IGl0ZW0uaWQsXHJcblxyXG4gICAgICBQcm9kdWN0SUQ6IGl0ZW0ucHJvZHVjdF9pZCB8fCBpdGVtLnByb2R1Y3Q/LnZhbHVlLCAvLyBQcm9kdWN0SURcclxuICAgICAgUHJvZHVjdE5hbWU6IGl0ZW0ucHJvZHVjdD8ubGFiZWwgfHwgaXRlbS5wcm9kdWN0Py52YWx1ZSwgLy8gUHJvZHVjdElEXHJcbiAgICAgIFNpemU6IGl0ZW0uc2l6ZT8ubGFiZWwgfHwgbnVsbCwgLy8gU2l6ZVxyXG4gICAgICAvLyBTaXplSUQ6IGl0ZW0uc2l6ZT8udmFsdWUgfHwgbnVsbCwgLy9UT0RPIG5vIHNpemUgaGFuZGVsXHJcbiAgICAgIFNpemVJRDpcclxuICAgICAgICBpdGVtLnNpemU/LnZhbHVlICYmICFpc05hTihOdW1iZXIoaXRlbS5zaXplLnZhbHVlKSlcclxuICAgICAgICAgID8gTnVtYmVyKGl0ZW0uc2l6ZS52YWx1ZSlcclxuICAgICAgICAgIDogbnVsbCwgLy9UT0RPIG5vIHNpemUgaGFuZGVsXHJcbiAgICAgIFF1YW50aXR5OiBOdW1iZXIoaXRlbS5xdWFudGl0eSkgfHwgMCwgLy8gUXVhbnRpdHkgKG1ha2Ugc3VyZSBpdCdzIG51bWVyaWMpXHJcbiAgICAgIE5hbWVGb3JQcmludGluZzogaXRlbS5uYW1lRm9yUHJpbnRpbmcgfHwgXCJcIiwgLy8gTmFtZUZvclByaW50aW5nXHJcbiAgICAgIENvbW1lbnRzOiBpdGVtLmNvbW1lbnRzIHx8IFwiXCIsIC8vIENvbW1lbnRzXHJcbiAgICB9KSk7XHJcblxyXG4gICAgY29uc3QgYXBpUGF5bG9hZCA9IHtcclxuICAgICAgcmVxdWVzdElkOiBwcGVJZCxcclxuICAgICAgYWN0aW9uX2lkOiBhY3Rpb25faWQsXHJcbiAgICAgIHN1Ym1pdHRlclVzZXJJZDogdXNlckRhdGEudXNlcl9pZCxcclxuICAgICAgdXNlcm5hbWU6IHVzZXJEYXRhPy5lbWFpbCB8fCBcInN5c3RlbVwiLFxyXG4gICAgICB0YXJnZXRTaXRlSWQ6IHNpdGUudmFsdWUsXHJcbiAgICAgIHJlcXVpcmVkRGF0ZTogcmVxdWlyZWREYXRlLFxyXG4gICAgICBpdGVtczogaXRlbXMsXHJcbiAgICAgIGNvbW1lbnQ6IGNhbmNlbGxlZFJlYXNvbmFwaSxcclxuICAgICAgb3JpZ25hdG9yRW1haWw6IGVtYWlsT2ZPcmlnaW5hdG9yLFxyXG4gICAgfTtcclxuICAgIGNvbnNvbGUubG9nKFxyXG4gICAgICBcImFwaVBheWxvYWQtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVwiLFxyXG4gICAgICBhcGlQYXlsb2FkXHJcbiAgICApO1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIGZldGNoKGAke3NlcnZlckFkZHJlc3N9cHBlLWNvbnN1bWFibGVzL3VwZGF0ZS1vcmRlci1yZXF1ZXN0YCwge1xyXG4gICAgICAgIG1ldGhvZDogXCJQb3N0XCIsXHJcbiAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgXCJjb250ZW50LXR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXHJcbiAgICAgICAgfSxcclxuICAgICAgICBjcmVkZW50aWFsczogXCJpbmNsdWRlXCIsXHJcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoYXBpUGF5bG9hZCksXHJcbiAgICAgIH0pXHJcbiAgICAgICAgLnRoZW4oKHJlcykgPT4ge1xyXG4gICAgICAgICAgaWYgKHJlcy5zdGF0dXMgPT09IDIwMCkge1xyXG4gICAgICAgICAgICByZXR1cm4gcmVzLmpzb24oKTtcclxuICAgICAgICAgIH0gZWxzZSBpZiAocmVzLnN0YXR1cyA9PT0gNDAxKSB7XHJcbiAgICAgICAgICAgIHRvYXN0LmVycm9yKFwiWW91ciBzZXNzaW9uIGhhcyBleHBpcmVkLiBQbGVhc2UgbG9nIGluIGFnYWluLlwiKTtcclxuICAgICAgICAgICAgc2V0VGltZW91dChhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgICAgICAgYXdhaXQgbG9nb3V0KCk7XHJcbiAgICAgICAgICAgICAgY29uc3QgcmVkaXJlY3RVcmwgPSBgL2xvZ2luP3JlZGlyZWN0PSR7ZW5jb2RlVVJJQ29tcG9uZW50KFxyXG4gICAgICAgICAgICAgICAgd2luZG93LmxvY2F0aW9uLnBhdGhuYW1lXHJcbiAgICAgICAgICAgICAgKX1gO1xyXG4gICAgICAgICAgICAgIHJvdXRlci5wdXNoKHJlZGlyZWN0VXJsKTtcclxuICAgICAgICAgICAgfSwgMzAwMCk7XHJcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xyXG4gICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgdG9hc3QuZXJyb3IoXCJGYWlsZWQgdG8gc2F2ZSBwcm9kdWN0LlwiKTtcclxuICAgICAgICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSlcclxuICAgICAgICAudGhlbigoanNvbikgPT4ge1xyXG4gICAgICAgICAgaWYgKGpzb24gJiYganNvbi5tc2cpIHtcclxuICAgICAgICAgICAgdG9hc3Quc3VjY2Vzcyhqc29uLm1zZywge1xyXG4gICAgICAgICAgICAgIHBvc2l0aW9uOiBcInRvcC1yaWdodFwiLFxyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgICAgICAgcm91dGVyLnJlcGxhY2UoXCIvcHBlLWNvbnN1bWFibGVcIik7XHJcbiAgICAgICAgICAgIH0sIDIwMDApO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0pO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5sb2coXCJlcnJvcmluIHN1Ym1pdHRpbmcgdGhlIG9yZGVyXCIsIGVycm9yKTtcclxuICAgIH1cclxuICAgIHNldExvYWRpbmcoZmFsc2UpO1xyXG4gICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xyXG4gICAgICB0b2FzdC5zdWNjZXNzKFwiUmVxdWVzdCBzdWJtaXR0ZWQhXCIpO1xyXG4gICAgICByb3V0ZXIucHVzaChcIi9wcGUtY29uc3VtYWJsZVwiKTtcclxuICAgIH0sIDEwMDApO1xyXG4gIH07XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHtcclxuICAgICAgQ29va2llcy5zZXQoXCJyYXdXYXJuaW5nXCIsIHRydWUsIHsgZXhwaXJlczogMzY1IH0pO1xyXG4gICAgICBDb29raWVzLnJlbW92ZShcImZpbmlzaFdhcm5pbmdcIik7XHJcbiAgICB9LCAyMDAwKTtcclxuXHJcbiAgICBpZiAocGFnZVR5cGUgPT0gXCJ1cGRhdGVcIikge1xyXG4gICAgICBzZXRJc0VkaXQodHJ1ZSk7XHJcbiAgICB9XHJcbiAgICBpZiAocGFnZVR5cGUgPT0gXCJhZGRcIiAmJiB1c2VyRGF0YSkge1xyXG4gICAgICBzZXROYW1lT2ZPcmlnaW5hdG9yKHVzZXJEYXRhLm5hbWUpO1xyXG4gICAgfVxyXG4gIH0sIFswXSk7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8PlxyXG4gICAgICA8Zm9ybVxyXG4gICAgICAgIG9uU3VibWl0PXtoYW5kbGVTdWJtaXR9XHJcbiAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBqdXN0aWZ5LXN0YXJ0IG1heC13LWZ1bGwgIG14LWF1dG8gbXItMTRcIlxyXG4gICAgICA+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJcIj5cclxuICAgICAgICAgIHsvKiA8aDIgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRyYWNraW5nLXdpZGUgbWItNCBwbC0yXCI+XHJcbiAgICAgICAgICAgIHtwYWdlVHlwZSA9PT0gXCJhZGRcIlxyXG4gICAgICAgICAgICAgID8gXCJBZGQgUFBFL0NvbnN1bWFibGUgT3JkZXJcIlxyXG4gICAgICAgICAgICAgIDogXCJFZGl0IFBQRS9Db25zdW1hYmxlIE9yZGVyXCJ9XHJcbiAgICAgICAgICA8L2gyPiAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXJvdyBnYXAtOCBiZy13aGl0ZSBwLTYgcm91bmRlZC1sZyBzaGFkb3ctWzBfMF8xcHhfcmdiYSgwLDAsMCwwLjEpXVwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbFwiPlxyXG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJsYWJlbHMgbWItMlwiPkZ1bGwgTmFtZTwvbGFiZWw+XHJcbiAgICAgICAgICAgICAgPGlucHV0XHJcbiAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXHJcbiAgICAgICAgICAgICAgICB2YWx1ZT17bmFtZU9mT3JpZ2luYXRvciB8fCBcIlwifVxyXG4gICAgICAgICAgICAgICAgZGlzYWJsZWRcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHctZnVsbCBweC00IHRleHQtZ3JheS01MDAgYm9yZGVyIHJvdW5kZWQtbGcgZm9ybS1pbnB1dFwiXHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsXCI+XHJcbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImxhYmVscyBtYi0yXCI+RW1haWw8L2xhYmVsPlxyXG4gICAgICAgICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxyXG4gICAgICAgICAgICAgICAgdmFsdWU9e2VtYWlsT2ZPcmlnaW5hdG9yIHx8IHVzZXJEYXRhPy5lbWFpbCB8fCBcIlwifVxyXG4gICAgICAgICAgICAgICAgZGlzYWJsZWRcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHctZnVsbCBweC00IHRleHQtZ3JheS01MDAgYm9yZGVyIHJvdW5kZWQtbGcgZm9ybS1pbnB1dFwiXHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsXCI+XHJcbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImxhYmVscyBtYi0yXCI+Q3JlYXRlZCBEYXRlPC9sYWJlbD5cclxuICAgICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICAgIHR5cGU9XCJkYXRlXCJcclxuICAgICAgICAgICAgICAgIHZhbHVlPXtuZXcgRGF0ZSgpLnRvTG9jYWxlRGF0ZVN0cmluZyhcImVuLUNBXCIpfVxyXG4gICAgICAgICAgICAgICAgZGlzYWJsZWRcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHctZnVsbCBweC00IHRleHQtZ3JheS01MDAgYm9yZGVyIHJvdW5kZWQtbGcgZm9ybS1pbnB1dFwiXHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsXCI+XHJcbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImxhYmVscyBtYi0yXCI+XHJcbiAgICAgICAgICAgICAgICBTaXRlPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1yZWQtNTAwIG1sLVsycHhdXCI+Kjwvc3Bhbj5cclxuICAgICAgICAgICAgICA8L2xhYmVsPlxyXG4gICAgICAgICAgICAgIDxTZWxlY3RcclxuICAgICAgICAgICAgICAgIHZhbHVlPXtzaXRlfVxyXG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgIHNldFNpdGUoKTtcclxuICAgICAgICAgICAgICAgICAgaGFuZGxlU2l0ZUFkZChlKTtcclxuICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICBvcHRpb25zPXtzaXRlRGF0YX1cclxuICAgICAgICAgICAgICAgIGlzRGlzYWJsZWQ9e3NpdGVzPy5sZW5ndGggPT09IDEgfHwgc3VibWl0dGVkfVxyXG4gICAgICAgICAgICAgICAgc3R5bGVzPXtjdXN0b21TZWxlY3RTdHlsZXN9XHJcbiAgICAgICAgICAgICAgICBpc0NsZWFyYWJsZT17dHJ1ZX1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIiB3LWZ1bGxcIlxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAge2Vycm9ycy5zaXRlICYmIDxwIHN0eWxlPXt7IGNvbG9yOiBcIiNlZjQ0NDRcIiB9fT57ZXJyb3JzLnNpdGV9PC9wPn1cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsXCI+XHJcbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImxhYmVscyBtYi0yXCI+XHJcbiAgICAgICAgICAgICAgICBSZXF1aXJlZCBEYXRlIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMFwiPio8L3NwYW4+XHJcbiAgICAgICAgICAgICAgPC9sYWJlbD5cclxuICAgICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICAgIHR5cGU9XCJkYXRlXCJcclxuICAgICAgICAgICAgICAgIG1pbj17bmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNwbGl0KFwiVFwiKVswXX1cclxuICAgICAgICAgICAgICAgIHZhbHVlPXtyZXF1aXJlZERhdGV9XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgc2V0UmVxdWlyZWREYXRlKGUudGFyZ2V0LnZhbHVlKTtcclxuICAgICAgICAgICAgICAgICAgdmFsaWRhdGVSZXF1aXJlZERhdGUoZS50YXJnZXQudmFsdWUpO1xyXG4gICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHctZnVsbCBweC00IGJvcmRlciByb3VuZGVkLWxnIGZvcm0taW5wdXRcIlxyXG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3N1Ym1pdHRlZH1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIHtkYXRlV2FybmluZyAmJiAoXHJcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LW9yYW5nZS01MDAgdGV4dC1zbVwiPntkYXRlV2FybmluZ308L3NwYW4+XHJcbiAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICB7ZXJyb3JzLnJlcXVpcmVkRGF0ZSAmJiAoXHJcbiAgICAgICAgICAgICAgICA8cCBzdHlsZT17eyBjb2xvcjogXCIjZWY0NDQ0XCIgfX0+e2Vycm9ycy5yZXF1aXJlZERhdGV9PC9wPlxyXG4gICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICB7LyogPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGxcIj5cclxuICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImxhYmVscyBtYi0yXCI+XHJcbiAgICAgICAgICAgICAgUmVxdWlyZWQgRGF0ZSA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC01MDBcIj4qPC9zcGFuPlxyXG4gICAgICAgICAgICA8L2xhYmVsPlxyXG4gICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICB0eXBlPVwiZGF0ZVwiXHJcbiAgICAgICAgICAgICAgbWluPXtuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc3BsaXQoXCJUXCIpWzBdfVxyXG4gICAgICAgICAgICAgIHZhbHVlPXtyZXF1aXJlZERhdGV9XHJcbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRSZXF1aXJlZERhdGUoZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHctZnVsbCBweC00IGJvcmRlciByb3VuZGVkLWxnIGZvcm0taW5wdXRcIlxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgPC9kaXY+ICovfVxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtM1wiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtcm93IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1lbmQgbXktNFwiPlxyXG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZmxleCBtYi0xIGZvbnQtc2VtaWJvbGQgdHJhY2tpbmctd2lkZXIgdGV4dC1iYXNlIHBsLTJcIj5cclxuICAgICAgICAgICAgICBJdGVtc1xyXG4gICAgICAgICAgICA8L2gzPlxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIG1iLTI4cHggYmctd2hpdGUgcC02IHJvdW5kZWQtbGcgcHktOCBzaGFkb3ctWzBfMF8ycHhfcmdiYSgwLDAsMCwwLjIpXVwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1yb3cgZ2FwLTQgbWItNFwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xLzRcIj5cclxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJsYWJlbHMgbWItMlwiPlxyXG4gICAgICAgICAgICAgICAgICBQcm9kdWN0IDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMFwiPio8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxyXG4gICAgICAgICAgICAgICAgPFNlbGVjdFxyXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Y3VycmVudEl0ZW0ucHJvZHVjdH1cclxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyh2YWwpID0+IGhhbmRsZUN1cnJlbnRJdGVtQ2hhbmdlKFwicHJvZHVjdFwiLCB2YWwpfVxyXG4gICAgICAgICAgICAgICAgICBvcHRpb25zPXtwcm9kdWN0c0RhdGF9IC8ve3Byb2R1Y3RzRGF0YS5Qcm9kdWN0TmFtZX0gLy9UT0RPXHJcbiAgICAgICAgICAgICAgICAgIHN0eWxlcz17Y3VzdG9tU2VsZWN0U3R5bGVzfVxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyZWFjdFNlbGVjdEN1c3RvbSB3LWZ1bGxcIlxyXG4gICAgICAgICAgICAgICAgICBpc1NlYXJjaGFibGVcclxuICAgICAgICAgICAgICAgICAgaXNDbGVhcmFibGVcclxuICAgICAgICAgICAgICAgICAgaXNEaXNhYmxlZD17c3VibWl0dGVkfVxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMS82XCI+XHJcbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwibGFiZWxzIG1iLTJcIj5cclxuICAgICAgICAgICAgICAgICAgU2l6ZVxyXG4gICAgICAgICAgICAgICAgICA8c3BhblxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHRleHQtcmVkLTUwMCAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgIWN1cnJlbnRJdGVtLnByb2R1Y3Q/LnNpemVfcmVxdWlyZWQgfHxcclxuICAgICAgICAgICAgICAgICAgICAgICFjdXJyZW50SXRlbS5wcm9kdWN0Py5zaXplcz8ubGVuZ3RoIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICBzdWJtaXR0ZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgPyBcImhpZGRlblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogXCJcIlxyXG4gICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgKlxyXG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxyXG4gICAgICAgICAgICAgICAgPFNlbGVjdFxyXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Y3VycmVudEl0ZW0uc2l6ZX1cclxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyh2YWwpID0+IGhhbmRsZUN1cnJlbnRJdGVtQ2hhbmdlKFwic2l6ZVwiLCB2YWwpfVxyXG4gICAgICAgICAgICAgICAgICBvcHRpb25zPXtjdXJyZW50SXRlbS5wcm9kdWN0Py5zaXplcyB8fCBbXX1cclxuICAgICAgICAgICAgICAgICAgc3R5bGVzPXtjdXN0b21TZWxlY3RTdHlsZXN9XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJlYWN0U2VsZWN0Q3VzdG9tIHctZnVsbFwiXHJcbiAgICAgICAgICAgICAgICAgIGlzU2VhcmNoYWJsZVxyXG4gICAgICAgICAgICAgICAgICBpc0NsZWFyYWJsZVxyXG4gICAgICAgICAgICAgICAgICBpc0Rpc2FibGVkPXtcclxuICAgICAgICAgICAgICAgICAgICAhY3VycmVudEl0ZW0ucHJvZHVjdD8uc2l6ZV9yZXF1aXJlZCB8fFxyXG4gICAgICAgICAgICAgICAgICAgICFjdXJyZW50SXRlbS5wcm9kdWN0Py5zaXplcz8ubGVuZ3RoIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgc3VibWl0dGVkXHJcbiAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEvNlwiPlxyXG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImxhYmVscyBtYi0yXCI+XHJcbiAgICAgICAgICAgICAgICAgIFF1YW50aXR5IDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMFwiPio8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxyXG4gICAgICAgICAgICAgICAgPGlucHV0XHJcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxyXG4gICAgICAgICAgICAgICAgICAvLyBtaW49ezB9XHJcbiAgICAgICAgICAgICAgICAgIG1heD17OTk5fVxyXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Y3VycmVudEl0ZW0ucXVhbnRpdHl9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT5cclxuICAgICAgICAgICAgICAgICAgICBoYW5kbGVDdXJyZW50SXRlbUNoYW5nZShcclxuICAgICAgICAgICAgICAgICAgICAgIFwicXVhbnRpdHlcIixcclxuICAgICAgICAgICAgICAgICAgICAgIGUudGFyZ2V0LnZhbHVlID4gOTk5ID8gOTk5IDogcGFyc2VJbnQoZS50YXJnZXQudmFsdWUpXHJcbiAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHctZnVsbCBweC00IGJvcmRlciByb3VuZGVkLWxnIGZvcm0taW5wdXRcIlxyXG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17c3VibWl0dGVkfVxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMS80XCI+XHJcbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwibGFiZWxzIG1iLTJcIj5OYW1lIGZvciBQcmludGluZzwvbGFiZWw+XHJcbiAgICAgICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxyXG4gICAgICAgICAgICAgICAgICBtYXhMZW5ndGg9XCI1MFwiIC8vVE9ETyBjaGFuZ2UgaW4gZGIgYWxzb1xyXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Y3VycmVudEl0ZW0ubmFtZUZvclByaW50aW5nfVxyXG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgaGFuZGxlQ3VycmVudEl0ZW1DaGFuZ2UoXCJuYW1lRm9yUHJpbnRpbmdcIiwgZS50YXJnZXQudmFsdWUpXHJcbiAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e1xyXG4gICAgICAgICAgICAgICAgICAgIGN1cnJlbnRJdGVtLnF1YW50aXR5ICE9PSAxIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgc3VibWl0dGVkIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgIWN1cnJlbnRJdGVtLnByb2R1Y3Q/Lm5hbWVfcHJpbnRhYmxlXHJcbiAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmxvY2sgdy1mdWxsIHB4LTQgYm9yZGVyIHJvdW5kZWQtbGcgZm9ybS1pbnB1dFwiXHJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiT25seSBpZiBxdWFudGl0eSA9IDFcIlxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMS80XCI+XHJcbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwibGFiZWxzIG1iLTJcIj5Db21tZW50czwvbGFiZWw+XHJcbiAgICAgICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2N1cnJlbnRJdGVtLmNvbW1lbnRzfVxyXG4gICAgICAgICAgICAgICAgICBtYXhMZW5ndGg9XCI1MDBcIlxyXG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgaGFuZGxlQ3VycmVudEl0ZW1DaGFuZ2UoXCJjb21tZW50c1wiLCBlLnRhcmdldC52YWx1ZSlcclxuICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJkaXNhYmxlZDp0ZXh0LWdyYXktNDAwIGRpc2FibGVkOmJnLVsjRjZGM0YzXSBibG9jayB3LWZ1bGwgaC04IHB4LTQgYm9yZGVyIHJvdW5kZWQtbGcgZm9ybS1pbnB1dCByZXNpemUtbm9uZVwiXHJcbiAgICAgICAgICAgICAgICAgIHJvd3M9ezF9XHJcbiAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtzdWJtaXR0ZWR9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1lbmRcIj5cclxuICAgICAgICAgICAgICAgIHsvKiA8cD5DYW5jZWwgRWRpdDwvcD4gKi99XHJcbiAgICAgICAgICAgICAgICA8c3BhblxyXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtzYXZlQ3VycmVudEl0ZW19XHJcbiAgICAgICAgICAgICAgICAgIC8vIG9uQ2xpY2s9e2hhbmRsZVNhdmVDbGlja31cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHgtMiBweS0xIDJ4bDpweC0zLjUgYm9yZGVyIGJvcmRlci1za2luLXByaW1hcnkgcm91bmRlZC1tZCB0ZXh0LXNraW4tcHJpbWFyeSBjdXJzb3ItcG9pbnRlciAke1xyXG4gICAgICAgICAgICAgICAgICAgIHN1Ym1pdHRlZCA/IFwiaGlkZGVuXCIgOiBcIlwiXHJcbiAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17c3VibWl0dGVkfVxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8Rm9udEF3ZXNvbWVJY29uIGljb249e2ZhRmxvcHB5RGlza30gLz5cclxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIHsvKiBcclxuICAgICAgICAgICAgLy8jcmVnaW9uIGdyaWRcclxuICAgICAgICAgICAgKi99XHJcbiAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhZy10aGVtZS1hbHBpbmUgbXktWzI0cHhdXCJcclxuICAgICAgICAgICAgICBzdHlsZT17eyBoZWlnaHQ6IDI1MCwgd2lkdGg6IFwiMTAwJVwiIH19XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICA8QWdHcmlkUmVhY3RcclxuICAgICAgICAgICAgICAgIHJlZj17Z3JpZFJlZn1cclxuICAgICAgICAgICAgICAgIGNvbHVtbkRlZnM9e2NvbHVtbkRlZnN9XHJcbiAgICAgICAgICAgICAgICByb3dEYXRhPXtzYXZlZEl0ZW1zfVxyXG4gICAgICAgICAgICAgICAgcm93SGVpZ2h0PXszNX1cclxuICAgICAgICAgICAgICAgIGdldFJvd0NsYXNzPXtnZXRSb3dDbGFzc31cclxuICAgICAgICAgICAgICAgIG9uR3JpZFJlYWR5PXtvbkdyaWRSZWFkeX1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIHsvKiBcclxuICAgICAgICAgICAgLy8jZW5kcmVnaW9uIGdyaWRcclxuICAgICAgICAgICAgKi99XHJcblxyXG4gICAgICAgICAgICAgIHsvKiB7ZXJyb3JzW2BpdGVtXyR7aW5kZXh9X3Byb2R1Y3RgXSAmJiAoXHJcbiAgICAgICAgICAgICAgICA8cCBzdHlsZT17eyBjb2xvcjogXCIjZWY0NDQ0XCIgfX0+e2Vycm9yc1tgaXRlbV8ke2luZGV4fV9wcm9kdWN0YF19PC9wPlxyXG4gICAgICAgICAgICAgICAgKX0gKi99XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICB7cG9wdXBUeXBlID09PSBcInVuc2F2ZWRXYXJuaW5nXCIgJiYgKFxyXG4gICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICA8cD5cclxuICAgICAgICAgICAgICAgICAgT25lIGl0ZW0gaXMgYmVpbmcgZWRpdGVkLiBJZiB5b3UgY29udGludWUsIGNoYW5nZXMgd2lsbCBiZVxyXG4gICAgICAgICAgICAgICAgICBsb3N0LlxyXG4gICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktZW5kIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtjbG9zZU1vZGFsfVxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlciBweC00IHB5LTIgcm91bmRlZFwiXHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICBDYW5jZWxcclxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICBzYXZlQ3VycmVudEl0ZW0oKTsgLy8gZGlzY2FyZCBlZGl0aW5nIHJvd1xyXG4gICAgICAgICAgICAgICAgICAgICAgY2xvc2VNb2RhbCgpO1xyXG4gICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctYmx1ZS02MDAgdGV4dC13aGl0ZSBweC00IHB5LTIgcm91bmRlZFwiXHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICBTYXZlIEFueXdheVxyXG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXJvdyBqdXN0aWZ5LWVuZCBnYXAtNCByb3VuZGVkLWxnICBwLTQgbXQtMlwiPlxyXG4gICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcclxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgIHJvdXRlci5wdXNoKFwiL3BwZS1jb25zdW1hYmxlXCIpO1xyXG4gICAgICAgICAgICB9fSAvLyBoYW5kbGUgY2FuY2VsIHdpdGggbG9hZGVyIGFuZCBhbGxcclxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyIGJvcmRlci1za2luLXByaW1hcnkgdGV4dC1za2luLXByaW1hcnkgcm91bmRlZC1tZCBweC02XCJcclxuICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmd9XHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIENhbmNlbFxyXG4gICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxyXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgaWYgKHBhZ2VUeXBlID09PSBcImVkaXRcIikge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coXCJoYXNVbnNhdmVkQ2hhbmdlc1wiLCBoYXNVbnNhdmVkQ2hhbmdlcyk7XHJcbiAgICAgICAgICAgICAgICBpZiAoaGFzVW5zYXZlZENoYW5nZXMpIHtcclxuICAgICAgICAgICAgICAgICAgc2V0UG9wdXBUeXBlKFwiU2F2ZVwiKTtcclxuICAgICAgICAgICAgICAgICAgc2V0SXNPcGVuKHRydWUpO1xyXG4gICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgaGFuZGxlVXBkYXRlKDUpO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICBoYW5kbGVTdWJtaXQoNSk7XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZyB8fCByZXF1ZXN0U3RhdHVzID09PSAxfVxyXG4gICAgICAgICAgICBjbGFzc05hbWU9e2Bib3JkZXIgYm9yZGVyLXNraW4tcHJpbWFyeSB0ZXh0LXNraW4tcHJpbWFyeSByb3VuZGVkLW1kIHB4LTYgJHtcclxuICAgICAgICAgICAgICByZXF1ZXN0U3RhdHVzICE9PSA1ID8gXCJoaWRkZW5cIiA6IFwiXCJcclxuICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIFNhdmVcclxuICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcclxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgIGlmIChoYXNVbnNhdmVkQ2hhbmdlcykge1xyXG4gICAgICAgICAgICAgICAgc2V0UG9wdXBUeXBlKFwic3VibWl0V2hpbGVFZGl0aW5nXCIpO1xyXG4gICAgICAgICAgICAgICAgc2V0SXNPcGVuKHRydWUpO1xyXG4gICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICBpZiAoIXZhbGlkYXRlKCkpIHJldHVybjtcclxuICAgICAgICAgICAgICAgIHNldFBvcHVwVHlwZShcInN1Ym1pdFwiKTtcclxuICAgICAgICAgICAgICAgIHNldElzT3Blbih0cnVlKTtcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT17YGJvcmRlciBib3JkZXItc2tpbi1wcmltYXJ5IGJnLXNraW4tcHJpbWFyeSB0ZXh0LXdoaXRlIHJvdW5kZWQtbWQgcHgtNiBmb250LW1lZGl1bSAke1xyXG4gICAgICAgICAgICAgIHJlcXVlc3RTdGF0dXMgIT09IDUgPyBcImhpZGRlblwiIDogXCJcIlxyXG4gICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmcgfHwgcmVxdWVzdFN0YXR1cyA9PT0gMX1cclxuICAgICAgICAgID5cclxuICAgICAgICAgICAgU3VibWl0XHJcbiAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXHJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICBpZiAoIXZhbGlkYXRlKCkpIHJldHVybjtcclxuICAgICAgICAgICAgICBzZXRQb3B1cFR5cGUoXCJyZWplY3RcIik7XHJcbiAgICAgICAgICAgICAgc2V0SXNPcGVuKHRydWUpO1xyXG4gICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICBjbGFzc05hbWU9e2BiZy1yZWQtNjAwIHRleHQtd2hpdGUgcm91bmRlZC1tZCBweC02IGZvbnQtbWVkaXVtICR7XHJcbiAgICAgICAgICAgICAgIXJlcXVlc3RTdGF0dXNMaXN0LmluY2x1ZGVzKDEpID8gXCJoaWRkZW5cIiA6IFwiXCJcclxuICAgICAgICAgICAgfSAgJHtcclxuICAgICAgICAgICAgICB1c2VyRGF0YS5yb2xlX2lkID09PSA2IHx8XHJcbiAgICAgICAgICAgICAgdXNlckRhdGEucm9sZV9pZCA9PT0gMSB8fFxyXG4gICAgICAgICAgICAgIHVzZXJEYXRhLnJvbGVfaWQgPT09IDVcclxuICAgICAgICAgICAgICAgID8gXCJcIlxyXG4gICAgICAgICAgICAgICAgOiBcImhpZGRlblwiXHJcbiAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICBSZWplY3QgQWxsIFBlbmRpbmcgUmVxdWVzdHNcclxuICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcclxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgIGlmICghdmFsaWRhdGUoKSkgcmV0dXJuO1xyXG4gICAgICAgICAgICAgIHNldFBvcHVwVHlwZShcImFwcHJvdmVcIik7XHJcbiAgICAgICAgICAgICAgc2V0SXNPcGVuKHRydWUpO1xyXG4gICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICBjbGFzc05hbWU9e2BiZy1ncmVlbi02MDAgdGV4dC13aGl0ZSByb3VuZGVkLW1kIHB4LTYgZm9udC1tZWRpdW0gJHtcclxuICAgICAgICAgICAgICAhcmVxdWVzdFN0YXR1c0xpc3QuaW5jbHVkZXMoMSkgPyBcImhpZGRlblwiIDogXCJcIlxyXG4gICAgICAgICAgICB9ICAke1xyXG4gICAgICAgICAgICAgIHVzZXJEYXRhLnJvbGVfaWQgPT09IDYgfHxcclxuICAgICAgICAgICAgICB1c2VyRGF0YS5yb2xlX2lkID09PSAxIHx8XHJcbiAgICAgICAgICAgICAgdXNlckRhdGEucm9sZV9pZCA9PT0gNVxyXG4gICAgICAgICAgICAgICAgPyBcIlwiXHJcbiAgICAgICAgICAgICAgICA6IFwiaGlkZGVuXCJcclxuICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIEFwcHJvdmUgQWxsIFBlbmRpbmcgUmVxdWVzdHNcclxuICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcclxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgIC8vICAgaWYgKHBhZ2VUeXBlID09PSBcImFkZFwiKSB7XHJcbiAgICAgICAgICAgICAgLy8gICAgIGhhbmRsZVN1Ym1pdCgxKTtcclxuICAgICAgICAgICAgICAvLyAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgdXBkYXRlU2VsZWN0ZWRSb3dTdGF0dXMoNCwgMik7XHJcbiAgICAgICAgICAgICAgLy8gICB9XHJcbiAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT17YGJnLWdyZWVuLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbWQgcHgtNiBmb250LW1lZGl1bSAke1xyXG4gICAgICAgICAgICAgIHJlcXVlc3RTdGF0dXNMaXN0LmluY2x1ZGVzKDIpID8gXCJcIiA6IFwiaGlkZGVuXCJcclxuICAgICAgICAgICAgfSAgJHtcclxuICAgICAgICAgICAgICB1c2VyRGF0YS5yb2xlX2lkID09PSA2IHx8XHJcbiAgICAgICAgICAgICAgdXNlckRhdGEucm9sZV9pZCA9PT0gMSB8fFxyXG4gICAgICAgICAgICAgIHVzZXJEYXRhLnJvbGVfaWQgPT09IDVcclxuICAgICAgICAgICAgICAgID8gXCJcIlxyXG4gICAgICAgICAgICAgICAgOiBcImhpZGRlblwiXHJcbiAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAvLyBkaXNhYmxlZD17bG9hZGluZyB8fCByZXF1ZXN0U3RhdHVzID09PSAxfVxyXG4gICAgICAgICAgICAvLyBoaWRlID0ge3JlcXVlc3RTdGF0dXMgIT09IDF9XHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIE1hcmsgQWxsIEFwcHJvdmVkIFJlcXVlc3RzIHRvIE9yZGVyZWRcclxuICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Zvcm0+XHJcbiAgICAgIDxUcmFuc2l0aW9uIGFwcGVhciBzaG93PXtpc09wZW59IGFzPXtGcmFnbWVudH0+XHJcbiAgICAgICAgPERpYWxvZ1xyXG4gICAgICAgICAgYXM9XCJkaXZcIlxyXG4gICAgICAgICAgY2xhc3NOYW1lPVwicmVsYXRpdmUgei0xMFwiXHJcbiAgICAgICAgICBvbkNsb3NlPXsodmFsdWUpID0+IHtcclxuICAgICAgICAgICAgaWYgKHBvcHVwVHlwZSAhPT0gXCJyZWplY3RcIiB8fCBpc1ZhbGlkQ2FuY2VsUmVhc29uKSB7XHJcbiAgICAgICAgICAgICAgY2xvc2VNb2RhbCgpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9fVxyXG4gICAgICAgID5cclxuICAgICAgICAgIDxUcmFuc2l0aW9uLkNoaWxkXHJcbiAgICAgICAgICAgIGFzPXtGcmFnbWVudH1cclxuICAgICAgICAgICAgZW50ZXI9XCJlYXNlLW91dCBkdXJhdGlvbi0zMDBcIlxyXG4gICAgICAgICAgICBlbnRlckZyb209XCJvcGFjaXR5LTBcIlxyXG4gICAgICAgICAgICBlbnRlclRvPVwib3BhY2l0eS0xMDBcIlxyXG4gICAgICAgICAgICBsZWF2ZT1cImVhc2UtaW4gZHVyYXRpb24tMjAwXCJcclxuICAgICAgICAgICAgbGVhdmVGcm9tPVwib3BhY2l0eS0xMDBcIlxyXG4gICAgICAgICAgICBsZWF2ZVRvPVwib3BhY2l0eS0wXCJcclxuICAgICAgICAgID5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIGJnLWJsYWNrIGJnLW9wYWNpdHktMjUgYmFja2Ryb3AtYmx1ci1zbVwiIC8+XHJcbiAgICAgICAgICA8L1RyYW5zaXRpb24uQ2hpbGQ+XHJcblxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIG92ZXJmbG93LXktYXV0b1wiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG1pbi1oLWZ1bGwgcC00IHRleHQtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgPFRyYW5zaXRpb24uQ2hpbGRcclxuICAgICAgICAgICAgICAgIGFzPXtGcmFnbWVudH1cclxuICAgICAgICAgICAgICAgIGVudGVyPVwiZWFzZS1vdXQgZHVyYXRpb24tMzAwXCJcclxuICAgICAgICAgICAgICAgIGVudGVyRnJvbT1cIm9wYWNpdHktMCBzY2FsZS05NVwiXHJcbiAgICAgICAgICAgICAgICBlbnRlclRvPVwib3BhY2l0eS0xMDAgc2NhbGUtMTAwXCJcclxuICAgICAgICAgICAgICAgIGxlYXZlPVwiZWFzZS1pbiBkdXJhdGlvbi0yMDBcIlxyXG4gICAgICAgICAgICAgICAgbGVhdmVGcm9tPVwib3BhY2l0eS0xMDAgc2NhbGUtMTAwXCJcclxuICAgICAgICAgICAgICAgIGxlYXZlVG89XCJvcGFjaXR5LTAgc2NhbGUtOTVcIlxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxEaWFsb2cuUGFuZWwgY2xhc3NOYW1lPVwiIHctWzQ1JV0gdHJhbnNmb3JtIG92ZXJmbG93LWhpZGRlbiByb3VuZGVkLXhsIGJnLXdoaXRlICB0ZXh0LWxlZnQgYWxpZ24tbWlkZGxlIHNoYWRvdy14bCB0cmFuc2l0aW9uLWFsbFwiPlxyXG4gICAgICAgICAgICAgICAgICB7LyogPCEtLSBNb2RhbCBjb250ZW50IC0tPiAqL31cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvd1wiPlxyXG4gICAgICAgICAgICAgICAgICAgIHsvKiA8IS0tIE1vZGFsIGhlYWRlciAtLT4gKi99XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlbiBwLTggcm91bmRlZC10XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXJvdyB0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCAgaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgaXRlbXMtY2VudGVyIGJvcmRlciBib3JkZXItc2tpbi1wcmltYXJ5IHRleHQtc2tpbi1wcmltYXJ5IHctWzI1cHhdIGgtWzI1cHhdIHRleHQtY2VudGVyIGxlYWRpbmctNSByb3VuZGVkLWZ1bGwgdGV4dC1iYXNlIG1lLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Rm9udEF3ZXNvbWVJY29uIGljb249e2ZhSW5mb30gLz57XCIgXCJ9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj57XCIgXCJ9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtgQ29uZmlybSAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHBvcHVwVHlwZSA9PSBcInN1Ym1pdFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwiU3VibWlzc2lvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHBvcHVwVHlwZSA9PSBcInJlamVjdFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwiUmVqZWN0aW9uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogcG9wdXBUeXBlID09IFwiYXBwcm92ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwiQXBwcm92YWxcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBwb3B1cFR5cGUgPT0gXCJtYXJrT3JkZXJlZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwibWFya09yZGVyZWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBwb3B1cFR5cGUgPT0gXCJTYXZlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJTYXZlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogcG9wdXBUeXBlID09IFwic3VibWl0V2hpbGVFZGl0aW5nXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJTdWJtaXNzaW9uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCIgXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2gzPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtjbG9zZU1vZGFsfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ibGFjayBiZy10cmFuc3BhcmVudCBob3ZlcjpiZy1ncmF5LTIwMCBob3Zlcjp0ZXh0LWdyYXktOTAwIHJvdW5kZWQtbGcgdGV4dC14bCB3LTggaC04IG1sLWF1dG8gaW5saW5lLWZsZXgganVzdGlmeS1jZW50ZXIgaXRlbXMtY2VudGVyIFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGRhdGEtbW9kYWwtaGlkZT1cImRlZmF1bHQtbW9kYWxcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8Rm9udEF3ZXNvbWVJY29uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaWNvbj17ZmFYbWFya31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXNraW4tcHJpbWFyeVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+e1wiIFwifVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgey8qIDwhLS0gTW9kYWwgYm9keSAtLT4gKi99XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTggcHktMCBzcGFjZS15LTZcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtYmFzZSB4bDp0ZXh0LW1kIDJ4bDp0ZXh0LWxnIGxlYWRpbmctcmVsYXhlZCBtdC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIEFyZSB5b3Ugc3VyZSB5b3Ugd2FudCB0b3tcIiBcIn1cclxuICAgICAgICAgICAgICAgICAgICAgICAge3BvcHVwVHlwZSA9PSBcInN1Ym1pdFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcInN1Ym1pdFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgOiBwb3B1cFR5cGUgPT0gXCJyZWplY3RcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJyZWplY3RcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogcG9wdXBUeXBlID09IFwiYXBwcm92ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcImFwcHJvdmVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogcG9wdXBUeXBlID09IFwibWFya09yZGVyZWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJtYXJrIGFwcHJvdmVkIGl0ZW1zIGFzIG9yZGVyZWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogcG9wdXBUeXBlID09IFwiU2F2ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcInNhdmU/IHRoZXJlIGlzIGEgaXRlbSBiZWluZyBlZGl0ZWQsXFxuIGl0IHdpbGwgYmUgbG9zdCBpZiB5b3UgU2F2ZSBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogcG9wdXBUeXBlID09IFwic3VibWl0V2hpbGVFZGl0aW5nXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwiU3VibWl0PyB0aGVyZSBpcyBhIGl0ZW0gYmVpbmcgZWRpdGVkLFxcbiBpdCB3aWxsIGJlIGxvc3QgaWYgeW91IFN1Ym1pdCBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogXCIgXCJ9e1wiIFwifVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzIHJlcXVlc3Q/e1wiIFwifVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9wPntcIiBcIn1cclxuICAgICAgICAgICAgICAgICAgICAgIHtwb3B1cFR5cGUgPT0gXCJyZWplY3RcIiAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIDxoMz5yZWplY3Rpb24gcmVhc29uPC9oMz4gKi99XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRleHRhcmVhXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHctZnVsbCByb3VuZGVkLW1kIHAtMiBweC0zICBib3JkZXIgYm9yZGVyLWxpZ2h0LWdyYXkyXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJvd3M9XCI4XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjYW5jZWxsZWRSZWFzb25hcGl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0Q2FuY2VsbGVkUmVhc29uYXBpKGUudGFyZ2V0LnZhbHVlKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGUudGFyZ2V0LnZhbHVlKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0SXNWYWxpZENhbmNlbFJlYXNvbih0cnVlKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQmx1cj17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdHJpbW1lZFZhbHVlID0gdHJpbUlucHV0VGV4dChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlLnRhcmdldC52YWx1ZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRDYW5jZWxsZWRSZWFzb25hcGkodHJpbW1lZFZhbHVlKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlByb3ZpZGUgcmVhc29uIGZvciByZWplY3Rpb24uLi5cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbWF4TGVuZ3RoPVwiNTAwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+PC90ZXh0YXJlYT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICB7cG9wdXBUeXBlID09IFwicmVqZWN0XCIgJiYgIWlzVmFsaWRDYW5jZWxSZWFzb24gJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1yZWQtNTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFBsZWFzZSBQcm92aWRlIHJlYXNvbiBmb3IgY2FuY2VsbGF0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIHsvKiA8IS0tIE1vZGFsIGZvb3RlciAtLT4gKi99XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWVuZCBwLTYgc3BhY2UteC0yIGp1c3RpZnktZW5kXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbG9zZU1vZGFsKCksIHNldENhbmNlbGxlZFJlYXNvbmFwaShcIlwiKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgZGF0YS1tb2RhbC1oaWRlPVwiZGVmYXVsdC1tb2RhbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJib3JkZXIgYm9yZGVyLXNraW4tcHJpbWFyeSB0ZXh0LXNraW4tcHJpbWFyeSBmb2N1czpyaW5nLTQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctYmx1ZS0zMDAgZm9udC1tZWRpdW0gcm91bmRlZC1tZCB0ZXh0LW1kIHB4LTYgcHktMiB0ZXh0LWNlbnRlciBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBDYW5jZWxcclxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcG9wdXBUeXBlID09PSBcInJlamVjdFwiICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAhY2FuY2VsbGVkUmVhc29uYXBpPy50cmltKClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICApIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldElzVmFsaWRDYW5jZWxSZWFzb24oZmFsc2UpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAocGFnZVR5cGUgPT0gXCJhZGRcIikge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHBvcHVwVHlwZSA9PSBcInN1Ym1pdFwiKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZVN1Ym1pdCgxKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAocG9wdXBUeXBlID09PSBcInN1Ym1pdFdoaWxlRWRpdGluZ1wiKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZVN1Ym1pdCgxKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHBvcHVwVHlwZSA9PSBcInN1Ym1pdFwiKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZVVwZGF0ZSgxKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAocG9wdXBUeXBlID09PSBcInJlamVjdFwiKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHVwZGF0ZVNlbGVjdGVkUm93U3RhdHVzKDMsIDEpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIGlmIChwb3B1cFR5cGUgPT09IFwiYXBwcm92ZVwiKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHVwZGF0ZVNlbGVjdGVkUm93U3RhdHVzKDIsIDEpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIGlmIChwb3B1cFR5cGUgPT09IFwiU2F2ZVwiKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZVVwZGF0ZSg1KTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAocG9wdXBUeXBlID09PSBcIm1hcmtPcmRlcmVkXCIpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdXBkYXRlU2VsZWN0ZWRSb3dTdGF0dXMoNCwgMik7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHBvcHVwVHlwZSA9PT0gXCJzdWJtaXRXaGlsZUVkaXRpbmdcIikge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVVcGRhdGUoMSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNldENhbmNlbGxlZFJlYXNvbmFwaShcIlwiKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbG9zZU1vZGFsKCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGRhdGEtbW9kYWwtaGlkZT1cImRlZmF1bHQtbW9kYWxcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBiZy1za2luLXByaW1hcnkgaG92ZXI6Ymctc2tpbi1wcmltYXJ5IGZvY3VzOnJpbmctNCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy1ibHVlLTMwMCBmb250LW1lZGl1bSByb3VuZGVkLW1kIHRleHQtbWQgcHgtNiBweS0yIHRleHQtY2VudGVyIGgtZnVsbCBib3JkZXIgYm9yZGVyLXNraW4tcHJpbWFyeVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIENvbnRpbnVlXHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L0RpYWxvZy5QYW5lbD5cclxuICAgICAgICAgICAgICA8L1RyYW5zaXRpb24uQ2hpbGQ+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9EaWFsb2c+XHJcbiAgICAgIDwvVHJhbnNpdGlvbj5cclxuICAgIDwvPlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBQcGVDb25zdW1hYmxlO1xyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VFZmZlY3QiLCJ1c2VSZWYiLCJ1c2VTdGF0ZSIsIkZyYWdtZW50IiwiU2VsZWN0IiwidG9hc3QiLCJ1c2VSb3V0ZXIiLCJDb29raWVzIiwiYXBpQ29uZmlnIiwiRm9udEF3ZXNvbWVJY29uIiwiZmFQZW5Ub1NxdWFyZSIsImZhSW5mbyIsImZhWG1hcmsiLCJmYVNxdWFyZUNoZWNrIiwiQWdHcmlkUmVhY3QiLCJmYUZsb3BweURpc2siLCJmYVRyYXNoIiwiRGlhbG9nIiwiVHJhbnNpdGlvbiIsImxvZ291dCIsImZhWG1hcmtDaXJjbGUiLCJmYUNpcmNsZUNoZWNrIiwicHJvZHVjdFN0YXR1c1JlbmRlcmVyIiwiZm9yRWFjaCIsImN1c3RvbVNlbGVjdFN0eWxlcyIsImNvbnRyb2wiLCJiYXNlIiwiaGVpZ2h0IiwibWluSGVpZ2h0IiwidmFsdWVDb250YWluZXIiLCJwcm92aWRlZCIsInBhZGRpbmciLCJpbnB1dCIsIm1hcmdpbiIsImluZGljYXRvclNlcGFyYXRvciIsImRpc3BsYXkiLCJpbmRpY2F0b3JzQ29udGFpbmVyIiwiZW1wdHlJdGVtIiwicHJvZHVjdCIsInNpemUiLCJxdWFudGl0eSIsIm5hbWVGb3JQcmludGluZyIsImNvbW1lbnRzIiwiUHBlQ29uc3VtYWJsZSIsInVzZXJEYXRhIiwicGFnZVR5cGUiLCJzaXRlcyIsIk9yZGVyUmVxdWVzdERhdGEiLCJwcGVJZCIsImN1cnJlbnRJdGVtIiwiaXNPcGVuIiwic2V0SXNPcGVuIiwibmFtZU9mT3JpZ2luYXRvciIsInNldE5hbWVPZk9yaWdpbmF0b3IiLCJlbWFpbE9mT3JpZ2luYXRvciIsInNldEVtYWlsT2ZPcmlnaW5hdG9yIiwic2l0ZURhdGEiLCJzZXRTaXRlRGF0YSIsIml0ZW1zIiwic2V0SXRlbXMiLCJyZXF1aXJlZERhdGUiLCJzZXRSZXF1aXJlZERhdGUiLCJjcmVhdGVkRGF0ZSIsInNldENyZWF0ZWREYXRlIiwic2l0ZSIsInNldFNpdGUiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInByb2R1Y3RzRGF0YSIsInNldFByb2R1Y3RzRGF0YSIsInJvdXRlciIsInNldEN1cnJlbnRJdGVtIiwic2F2ZWRJdGVtcyIsInNldFNhdmVkSXRlbXMiLCJncmlkUmVmIiwiZGF0ZVdhcm5pbmciLCJzZXREYXRlV2FybmluZyIsInJlcXVlc3RTdGF0dXMiLCJzZXRSZXF1ZXN0U3RhdHVzIiwicmVxdWVzdFN0YXR1c0xpc3QiLCJzZXRSZXF1ZXN0U3RhdHVzTGlzdCIsImVycm9ycyIsInNldEVycm9ycyIsInN1Ym1pdHRlZCIsInNldFN1Ym1pdHRlZCIsInBvcHVwVHlwZSIsInNldFBvcHVwVHlwZSIsImNhbmNlbGxlZFJlYXNvbmFwaSIsInNldENhbmNlbGxlZFJlYXNvbmFwaSIsImlzVmFsaWRDYW5jZWxSZWFzb24iLCJzZXRJc1ZhbGlkQ2FuY2VsUmVhc29uIiwiZWRpdGluZ1Jvd0lkIiwic2V0RWRpdGluZ1Jvd0lkIiwiZ3JpZEFwaSIsInNldEdyaWRBcGkiLCJoYXNVbnNhdmVkQ2hhbmdlcyIsInNldEhhc1Vuc2F2ZWRDaGFuZ2VzIiwiY2xvc2VNb2RhbCIsImUiLCJwcmV2ZW50RGVmYXVsdCIsInNldFRpbWVvdXQiLCJzZXQiLCJleHBpcmVzIiwicmVtb3ZlIiwibmFtZSIsImxlbmd0aCIsImFjdGlvbl9pZCIsInNpdGVfaWQiLCJsYWJlbCIsInNpdGVfbmFtZSIsInZhbHVlIiwicmVxdWlyZWRfZGF0ZSIsIkRhdGUiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJ5ZWFyIiwibW9udGgiLCJkYXkiLCJjcmVhdGVkX2F0IiwidXNlcl9uYW1lIiwiYWN0aW9uZWRfYnlfZW1haWwiLCJBcnJheSIsImlzQXJyYXkiLCJjb25zb2xlIiwibG9nIiwiU1RBVFVTX01BUCIsIm1hcHBlZEl0ZW1zIiwibWFwIiwiaXRlbSIsImlkIiwib3JkZXJfcmVxdWVzdF9pdGVtcyIsInByb2R1Y3RfaWQiLCJwcm9kdWN0X25hbWUiLCJuYW1lX2Zvcl9wcmludGluZyIsInVwZGF0ZUZsYWciLCJzdGF0dXMiLCJ1bmlxdWVWYWx1ZXMiLCJTZXQiLCJvblJvd1NlbGVjdGVkIiwiZXZlbnQiLCJub2RlIiwiaXNTZWxlY3RlZCIsImRhdGEiLCJvbkdyaWRSZWFkeSIsInBhcmFtcyIsImFwaSIsImdldFJvd0NsYXNzIiwiSWNvbnNSZW5kZXJlciIsInByb3BzIiwiaGFuZGxlRGVsZXRlIiwic2VsZWN0ZWRSb3ciLCJ1cGRhdGVkRGF0YSIsImZpbHRlciIsImhhbmRsZUVkaXQiLCJoYW5kbGVSb3dVcGRhdGUiLCJhY3Rpb25JZCIsInNldFNlbGVjdGVkUm93RGF0YSIsInNldFN0YXR1c0FjdGlvbiIsInNldFNob3dTdGF0dXNEaWFsb2ciLCJuYW1lRm9yUHJpbnRpbmdGbGFnIiwiZGl2IiwiY2xhc3NOYW1lIiwiYnV0dG9uIiwib25DbGljayIsImljb24iLCJjb2x1bW5EZWZzIiwiaGVhZGVyTmFtZSIsImZpZWxkIiwiZmxleCIsImhlYWRlckNsYXNzIiwiY2VsbFJlbmRlcmVyIiwiY2VsbFN0eWxlIiwianVzdGlmeUNvbnRlbnQiLCJwYWRkaW5nUmlnaHQiLCJoaWRlIiwiZmlsdGVyUGFyYW1zIiwidmFsdWVzIiwiZ2V0QWxsUHJvZHVjdHMiLCJzZXJ2ZXJBZGRyZXNzIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiY3JlZGVudGlhbHMiLCJ0aGVuIiwicmVzIiwianNvbiIsImVycm9yIiwicHVzaCIsIkVycm9yIiwiY2F0Y2giLCJtZXNzYWdlIiwiZ2V0QWxsU2l0ZXMiLCJmZXRjaFNpdGVzIiwiZm9ybWF0dGVkU2l0ZXMiLCJmZXRjaFByb2R1Y3RzIiwicHJvZHVjdHNKc29uIiwiZmlsdGVyT3V0SGlkZGVuUHJvZHVjdHMiLCJJc1Byb2R1Y3RIaWRkZW4iLCJmb3JtYXR0ZWRQcm9kdWN0cyIsIlByb2R1Y3RJZCIsIlByb2R1Y3ROYW1lIiwic2l6ZV9yZXF1aXJlZCIsIklzU2l6ZVJlcXVpcmVkIiwic2l6ZXMiLCJBdmFpbGFibGVTaXplcyIsInNwbGl0IiwidG9Mb3dlckNhc2UiLCJyZXBsYWNlIiwicHJvZHVjdENvZGUiLCJQcm9kdWN0Q29kZSIsInBhY2thZ2VRdWFudGl0eSIsIlBhY2thZ2VRdWFudGl0eSIsInByb2R1Y3RUeXBlIiwiUHJvZHVjdFR5cGUiLCJuYW1lX3ByaW50YWJsZSIsInZhbGlkYXRlIiwibmV3RXJyb3JzIiwiaGFzRXJyb3JzIiwiT2JqZWN0Iiwia2V5cyIsImVycm9yTWVzc2FnZXMiLCJlcnJvcnNMZW5ndGgiLCJoYW5kbGVTaXRlQWRkIiwicHJldiIsInVwZGF0ZWQiLCJ2YWxpZGF0ZVJlcXVpcmVkRGF0ZSIsInNlbGVjdGVkRGF0ZSIsInRvZGF5Iiwic2VsZWN0ZWQiLCJ0d29XZWVrc0Zyb21Ob3ciLCJzZXREYXRlIiwiZ2V0RGF0ZSIsInRyaW1JbnB1dFRleHQiLCJ0cmltIiwiaGFuZGxlQ2FuY2VsUmVhc29uIiwiaGFuZGxlU2F2ZUNsaWNrIiwic2F2ZUN1cnJlbnRJdGVtIiwiY291bnQiLCJub3ciLCJoYW5kbGVDdXJyZW50SXRlbUNoYW5nZSIsImhhbmRsZVN1Ym1pdCIsInN0YXR1c19JRCIsInJlcXVlc3RJdGVtSWQiLCJQcm9kdWN0SUQiLCJTaXplIiwiUXVhbnRpdHkiLCJOdW1iZXIiLCJOYW1lRm9yUHJpbnRpbmciLCJDb21tZW50cyIsImFwaVBheWxvYWQiLCJTdGF0dXNfaWQiLCJTdWJtaXR0ZXJVc2VySUQiLCJ1c2VyX2lkIiwiVGFyZ2V0U2l0ZUlEIiwiUmVxdWlyZWREYXRlIiwidXNlcm5hbWUiLCJlbWFpbCIsIm9yaWduYXRvckVtYWlsIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJyZWRpcmVjdFVybCIsImVuY29kZVVSSUNvbXBvbmVudCIsIndpbmRvdyIsImxvY2F0aW9uIiwicGF0aG5hbWUiLCJtc2ciLCJzdWNjZXNzIiwicG9zaXRpb24iLCJ1cGRhdGVTZWxlY3RlZFJvd1N0YXR1cyIsInVwZGF0ZWRTdGF0dXNJRCIsImV4aXN0aW5nU3RhdHVzSWQiLCJjb21tZW50IiwidG9CZVVwZGF0ZWQiLCJ3YXJuaW5nIiwidXBkYXRlUHJvbWlzZXMiLCJyZXF1ZXN0X25vIiwiUmVxdWVzdEl0ZW1JRCIsImNvbW1lbnRPblVwZGF0aW5nUmVxdWVzdCIsIlN1Ym1pdHRlckVtYWlsIiwiZXJyb3JUZXh0IiwidGV4dCIsIlByb21pc2UiLCJhbGwiLCJyZXN1bHRzIiwic3VjY2Vzc0NvdW50IiwicmVzdWx0IiwiZmluYWxseSIsImhhbmRsZVVwZGF0ZSIsInN0YXJ0c1dpdGgiLCJTaXplSUQiLCJpc05hTiIsInJlcXVlc3RJZCIsInN1Ym1pdHRlclVzZXJJZCIsInRhcmdldFNpdGVJZCIsInNldElzRWRpdCIsImZvcm0iLCJvblN1Ym1pdCIsInR5cGUiLCJkaXNhYmxlZCIsInNwYW4iLCJvbkNoYW5nZSIsIm9wdGlvbnMiLCJpc0Rpc2FibGVkIiwic3R5bGVzIiwiaXNDbGVhcmFibGUiLCJwIiwic3R5bGUiLCJjb2xvciIsIm1pbiIsInRvSVNPU3RyaW5nIiwidGFyZ2V0IiwiaDMiLCJ2YWwiLCJpc1NlYXJjaGFibGUiLCJtYXgiLCJwYXJzZUludCIsIm1heExlbmd0aCIsInBsYWNlaG9sZGVyIiwicm93cyIsIndpZHRoIiwicmVmIiwicm93RGF0YSIsInJvd0hlaWdodCIsImluY2x1ZGVzIiwicm9sZV9pZCIsImFwcGVhciIsInNob3ciLCJhcyIsIm9uQ2xvc2UiLCJDaGlsZCIsImVudGVyIiwiZW50ZXJGcm9tIiwiZW50ZXJUbyIsImxlYXZlIiwibGVhdmVGcm9tIiwibGVhdmVUbyIsIlBhbmVsIiwiZGF0YS1tb2RhbC1oaWRlIiwidGV4dGFyZWEiLCJvbkJsdXIiLCJ0cmltbWVkVmFsdWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/PpeConsumable.js\n"));

/***/ })

});